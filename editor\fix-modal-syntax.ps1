# PowerShell脚本来修复Modal语法错误

Write-Host "🚀 开始修复Modal语法错误..." -ForegroundColor Green

$fixedCount = 0
$srcPath = "src"

# 获取所有tsx和ts文件
$files = Get-ChildItem -Path $srcPath -Recurse -Include "*.tsx", "*.ts"

Write-Host "📁 找到 $($files.Count) 个文件" -ForegroundColor Blue

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # 修复语法错误：onCancel={() = destroyOnClose
    $pattern1 = 'onCancel=\{\(\)\s*=\s*destroyOnClose\s+keyboard=\{true\}\s+maskClosable=\{true\}\>\s*\{'
    if ($content -match $pattern1) {
        $content = $content -replace $pattern1, 'onCancel={() => {'
        
        # 添加属性到Modal标签的末尾
        $content = $content -replace '(\}\s*)\s*width=', "`$1`n        destroyOnClose`n        keyboard={true}`n        maskClosable={true}`n        width="
        $content = $content -replace '(\}\s*)\s*footer=', "`$1`n        destroyOnClose`n        keyboard={true}`n        maskClosable={true}`n        footer="
        $content = $content -replace '(\}\s*)\s*>', "`$1`n        destroyOnClose`n        keyboard={true}`n        maskClosable={true}>"
    }
    
    # 修复嵌套大括号问题
    $pattern2 = 'try\s*\{\s*\{'
    if ($content -match $pattern2) {
        $content = $content -replace $pattern2, 'try {'
    }
    
    # 修复缺少分号的问题
    $pattern3 = '(setIs\w+\(false\))\s*(?!\;)'
    $content = $content -replace $pattern3, '$1;'
    
    $pattern4 = '(set\w+\(false\))\s*(?!\;)'
    $content = $content -replace $pattern4, '$1;'
    
    $pattern5 = '(set\w+\(null\))\s*(?!\;)'
    $content = $content -replace $pattern5, '$1;'
    
    # 如果内容有变化，保存文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "✅ 已修复: $($file.FullName)" -ForegroundColor Green
        $fixedCount++
    }
}

Write-Host "`n✨ 修复完成！共修复了 $fixedCount 个文件" -ForegroundColor Green

if ($fixedCount -gt 0) {
    Write-Host "`n📋 修复内容包括:" -ForegroundColor Yellow
    Write-Host "  - 修复 onCancel={() = destroyOnClose 语法错误" -ForegroundColor White
    Write-Host "  - 正确放置 destroyOnClose、keyboard、maskClosable 属性" -ForegroundColor White
    Write-Host "  - 修复嵌套大括号问题" -ForegroundColor White
    Write-Host "  - 添加缺少的分号" -ForegroundColor White
}
