/**
 * 教程系列面板组件
 * 展示教程系列及其包含的教程
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Tag, 
  Button, 
  Typography, 
  Tooltip, 
  Progress, 
  Steps, 
  Divider, 
  Empty, 
  Skeleton,
  Badge,
  Modal,
  Space
} from 'antd';
import { 
  PlayCircleOutlined, 
  ReadOutlined, 
  CodeOutlined, 
  ArrowLeftOutlined, 
  CheckCircleOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  FireOutlined,
  BookOutlined,
  TagOutlined,
  LockOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { 
  tutorialRecommendationService, 
  TutorialItem, 
  TutorialSeries, 
  TutorialType, 
  TutorialDifficulty 
} from '../../services/TutorialRecommendationService';
import { TutorialService } from '../../services/TutorialService';
import { VideoTutorialService } from '../../services/VideoTutorialService';
import { ExampleProjectService } from '../../services/ExampleProjectService';
import './TutorialSeriesPanel.less';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

/**
 * 教程系列面板属性接口
 */
interface TutorialSeriesPanelProps {
  seriesId?: string;
  series?: TutorialSeries;
  onBack?: () => void;
  onTutorialSelect?: (item: TutorialItem) => void;
}

/**
 * 教程系列面板组件
 */
export const TutorialSeriesPanel: React.FC<TutorialSeriesPanelProps> = ({
  seriesId,
  series: initialSeries,
  onBack,
  onTutorialSelect}) => {
  const { t } = useTranslation();
  const [series, setSeries] = useState<TutorialSeries | undefined>(initialSeries);
  const [tutorials, setTutorials] = useState<TutorialItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [completedCount, setCompletedCount] = useState<number>(0);
  const [progressPercent, setProgressPercent] = useState<number>(0);
  const [showCertificateModal, setShowCertificateModal] = useState<boolean>(false);

  // 初始化
  useEffect(() => {
    if (seriesId || initialSeries) {
      loadSeriesData();
    }

    // 监听教程完成事件
    const handleItemCompleted = () => {
      loadSeriesData();
    };

    tutorialRecommendationService.on('itemCompleted', handleItemCompleted);

    return () => {
      tutorialRecommendationService.off('itemCompleted', handleItemCompleted);
    };
  }, [seriesId, initialSeries]);

  /**
   * 加载系列数据
   */
  const loadSeriesData = () => {
    setLoading(true);
    
    let currentSeries: TutorialSeries | undefined;
    
    if (initialSeries) {
      currentSeries = initialSeries;
    } else if (seriesId) {
      currentSeries = tutorialRecommendationService.getTutorialSeriesById(seriesId);
    }
    
    if (!currentSeries) {
      setLoading(false);
      return;
    }
    
    setSeries(currentSeries);
    
    // 获取系列中的教程项
    const seriesTutorials = tutorialRecommendationService.getTutorialsInSeries(currentSeries.id);
    setTutorials(seriesTutorials);
    
    // 计算完成进度
    const completed = seriesTutorials.filter(item => item.completed).length;
    setCompletedCount(completed);
    
    const progress = seriesTutorials.length > 0 
      ? Math.round((completed / seriesTutorials.length) * 100) 
      : 0;
    setProgressPercent(progress);
    
    // 设置当前步骤
    const firstIncomplete = seriesTutorials.findIndex(item => !item.completed);
    setCurrentStep(firstIncomplete >= 0 ? firstIncomplete : seriesTutorials.length - 1);
    
    setLoading(false);
    
    // 如果系列已完成，显示证书
    if (progress === 100 && completed > 0) {
      setShowCertificateModal(true);
    }
  };

  /**
   * 处理教程项点击
   */
  const handleTutorialItemClick = (item: TutorialItem) => {
    if (onTutorialSelect) {
      onTutorialSelect(item);
      return;
    }

    // 根据教程类型打开相应的教程
    switch (item.type) {
      case TutorialType.INTERACTIVE:
        const tutorialId = item.id.split(':')[1];
        TutorialService.getInstance().startTutorial(tutorialId);
        break;
      case TutorialType.VIDEO:
        const videoId = item.id.split(':')[1];
        VideoTutorialService.getInstance().openTutorial(videoId);
        break;
      case TutorialType.EXAMPLE:
        const exampleId = item.id.split(':')[1];
        ExampleProjectService.getInstance().markExampleAsViewed(exampleId);
        window.open(`/examples/${exampleId}/`, '_blank');
        break;
    }
  };

  /**
   * 获取教程类型图标
   */
  const getTutorialTypeIcon = (type: TutorialType) => {
    switch (type) {
      case TutorialType.INTERACTIVE:
        return <ReadOutlined />;
      case TutorialType.VIDEO:
        return <PlayCircleOutlined />;
      case TutorialType.EXAMPLE:
        return <CodeOutlined />;
      default:
        return <BookOutlined />;
    }
  };

  /**
   * 获取教程类型文本
   */
  const getTutorialTypeText = (type: TutorialType) => {
    switch (type) {
      case TutorialType.INTERACTIVE:
        return t('tutorials.types.interactive');
      case TutorialType.VIDEO:
        return t('tutorials.types.video');
      case TutorialType.EXAMPLE:
        return t('tutorials.types.example');
      default:
        return t('tutorials.types.unknown');
    }
  };

  /**
   * 获取难度标签颜色
   */
  const getDifficultyColor = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return 'green';
      case TutorialDifficulty.INTERMEDIATE:
        return 'blue';
      case TutorialDifficulty.ADVANCED:
        return 'orange';
      case TutorialDifficulty.EXPERT:
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取难度文本
   */
  const getDifficultyText = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return t('tutorials.difficulty.beginner');
      case TutorialDifficulty.INTERMEDIATE:
        return t('tutorials.difficulty.intermediate');
      case TutorialDifficulty.ADVANCED:
        return t('tutorials.difficulty.advanced');
      case TutorialDifficulty.EXPERT:
        return t('tutorials.difficulty.expert');
      default:
        return t('tutorials.difficulty.unknown');
    }
  };

  /**
   * 检查教程是否可用
   * 如果教程有前置条件，则检查前置条件是否已完成
   */
  const isTutorialAvailable = (item: TutorialItem): boolean => {
    if (!item.prerequisites || item.prerequisites.length === 0) {
      return true;
    }
    
    return item.prerequisites.every(prereqId => {
      const prereq = tutorialRecommendationService.getTutorialItemById(prereqId);
      return prereq?.completed === true;
    });
  };

  /**
   * 渲染教程项卡片
   */
  const renderTutorialItem = (item: TutorialItem, index: number) => {
    const isAvailable = isTutorialAvailable(item);
    
    return (
      <Card 
        className={`tutorial-item-card ${!isAvailable ? 'tutorial-locked' : ''}`}
        hoverable={isAvailable}
        onClick={() => isAvailable && handleTutorialItemClick(item)}
      >
        <div className="tutorial-item-number">{index + 1}</div>
        
        <Card.Meta
          avatar={
            <div className="tutorial-item-status">
              {item.completed ? (
                <CheckCircleOutlined className="completed-icon" />
              ) : !isAvailable ? (
                <LockOutlined className="locked-icon" />
              ) : (
                <Badge 
                  count={index === currentStep ? t('tutorials.next') : ''} 
                  style={{ backgroundColor: '#1890ff' }}
                />
              )}
            </div>
          }
          title={
            <div className="tutorial-item-title">
              <span>{item.title}</span>
              {item.popularity && item.popularity > 80 && (
                <Tooltip title={t('tutorials.popular')}>
                  <FireOutlined className="popular-icon" />
                </Tooltip>
              )}
            </div>
          }
          description={
            <div className="tutorial-item-description">
              <Paragraph ellipsis={{ rows: 2 }}>{item.description}</Paragraph>
              
              <div className="tutorial-item-meta">
                <Tooltip title={getTutorialTypeText(item.type)}>
                  <Tag icon={getTutorialTypeIcon(item.type)}>
                    {getTutorialTypeText(item.type)}
                  </Tag>
                </Tooltip>
                
                {item.duration && (
                  <Tooltip title={t('tutorials.duration')}>
                    <Tag icon={<ClockCircleOutlined />}>
                      {item.duration} {t('tutorials.minutes')}
                    </Tag>
                  </Tooltip>
                )}
                
                {!isAvailable && (
                  <Tooltip title={t('tutorials.prerequisitesRequired')}>
                    <Tag icon={<LockOutlined />} color="error">
                      {t('tutorials.locked')}
                    </Tag>
                  </Tooltip>
                )}
              </div>
              
              {item.progress !== undefined && item.progress > 0 && !item.completed && (
                <div className="tutorial-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${item.progress}%` }}
                    />
                  </div>
                  <Text type="secondary">{item.progress}%</Text>
                </div>
              )}
              
              {!isAvailable && item.prerequisites && item.prerequisites.length > 0 && (
                <div className="tutorial-prerequisites">
                  <Text type="secondary">{t('tutorials.prerequisites')}:</Text>
                  <ul>
                    {item.prerequisites.map((prereqId, idx) => {
                      const prereq = tutorialRecommendationService.getTutorialItemById(prereqId);
                      return (
                        <li key={idx}>
                          {prereq ? (
                            <span className={prereq.completed ? 'completed-prerequisite' : ''}>
                              {prereq.title}
                              {prereq.completed && <CheckCircleOutlined className="prereq-check" />}
                            </span>
                          ) : prereqId}
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )}
              
              {isAvailable && (
                <Button 
                  type="primary" 
                  className="start-tutorial-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTutorialItemClick(item);
                  }}
                >
                  {item.completed ? t('tutorials.review') : t('tutorials.start')}
                </Button>
              )}
            </div>
          }
        />
      </Card>
    );
  };

  /**
   * 渲染证书模态框
   */
  const renderCertificateModal = () => {
    if (!series) return null;
    
    return (
      <Modal
        title={t('tutorials.seriesCompleted')}
        open={showCertificateModal}
        onCancel={() => {
          try {
            setShowCertificateModal(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={[
          <Button key="close" onClick={() => setShowCertificateModal(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={600}
        className="certificate-modal"
      >
        <div className="certificate">
          <div className="certificate-header">
            <TrophyOutlined className="certificate-icon" />
            <Title level={2}>{t('tutorials.certificate')}</Title>
          </div>
          
          <div className="certificate-content">
            <Title level={3}>{t('tutorials.congratulations')}</Title>
            <Paragraph>
              {t('tutorials.certificateText', { name: '用户', series: series.title })}
            </Paragraph>
            
            <div className="certificate-details">
              <div className="certificate-detail">
                <Text strong>{t('tutorials.series')}:</Text>
                <Text>{series.title}</Text>
              </div>
              <div className="certificate-detail">
                <Text strong>{t('tutorials.completedTutorials')}:</Text>
                <Text>{completedCount}</Text>
              </div>
              <div className="certificate-detail">
                <Text strong>{t('tutorials.difficulty')}:</Text>
                <Tag color={getDifficultyColor(series.difficulty)}>
                  {getDifficultyText(series.difficulty)}
                </Tag>
              </div>
              <div className="certificate-detail">
                <Text strong>{t('tutorials.completedDate')}:</Text>
                <Text>{new Date().toLocaleDateString()}</Text>
              </div>
            </div>
          </div>
          
          <div className="certificate-footer">
            <div className="certificate-seal">
              <img src="/assets/images/tutorials/certificate-seal.png" alt="Certificate Seal" />
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  if (loading) {
    return (
      <div className="tutorial-series-panel">
        <div className="tutorial-series-skeleton">
          <Skeleton active avatar paragraph={{ rows: 2 }} />
          <Skeleton active avatar paragraph={{ rows: 3 }} />
          <Skeleton active avatar paragraph={{ rows: 3 }} />
        </div>
      </div>
    );
  }

  if (!series) {
    return (
      <div className="tutorial-series-panel">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('tutorials.seriesNotFound')}
        />
        {onBack && (
          <Button type="primary" onClick={onBack} icon={<ArrowLeftOutlined />}>
            {t('common.back')}
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="tutorial-series-panel">
      <div className="series-header">
        {onBack && (
          <Button 
            type="link" 
            className="back-button" 
            onClick={onBack}
            icon={<ArrowLeftOutlined />}
          >
            {t('common.back')}
          </Button>
        )}
        
        <Title level={3}>{series.title}</Title>
        
        <div className="series-meta">
          <Tag color={getDifficultyColor(series.difficulty)}>
            {getDifficultyText(series.difficulty)}
          </Tag>
          
          <Tag icon={<BookOutlined />}>
            {tutorials.length} {t('tutorials.items')}
          </Tag>
          
          {series.tags && series.tags.length > 0 && (
            <Tooltip title={series.tags.join(', ')}>
              <Tag icon={<TagOutlined />}>
                {series.tags.length} {t('tutorials.tags')}
              </Tag>
            </Tooltip>
          )}
        </div>
        
        <Paragraph>{series.description}</Paragraph>
        
        <div className="series-progress">
          <Progress 
            percent={progressPercent} 
            status={progressPercent === 100 ? 'success' : 'active'} 
            format={percent => `${percent}% ${t('tutorials.completed')}`}
          />
          <Text>
            {completedCount} / {tutorials.length} {t('tutorials.tutorialsCompleted')}
          </Text>
        </div>
      </div>
      
      <Divider />
      
      <div className="series-content">
        <Steps 
          direction="vertical" 
          current={currentStep}
          className="series-steps"
        >
          {tutorials.map((tutorial, index) => (
            <Step
              key={tutorial.id}
              title={tutorial.title}
              description={
                <div className="step-description">
                  <Space>
                    {getTutorialTypeIcon(tutorial.type)}
                    <Text>{getTutorialTypeText(tutorial.type)}</Text>
                    {tutorial.duration && (
                      <Text type="secondary">
                        <ClockCircleOutlined /> {tutorial.duration} {t('tutorials.minutes')}
                      </Text>
                    )}
                  </Space>
                </div>
              }
              status={
                tutorial.completed 
                  ? 'finish' 
                  : index === currentStep 
                    ? 'process' 
                    : 'wait'
              }
              icon={
                tutorial.completed 
                  ? <CheckCircleOutlined /> 
                  : !isTutorialAvailable(tutorial) 
                    ? <LockOutlined /> 
                    : null
              }
            />
          ))}
        </Steps>
        
        <div className="series-tutorials">
          <List
            dataSource={tutorials}
            renderItem={(item, index) => (
              <List.Item>
                {renderTutorialItem(item, index)}
              </List.Item>
            )}
          />
        </div>
      </div>
      
      {renderCertificateModal()}
    </div>
  );
};
