import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 批量修复Modal组件的语法错误
 * 主要修复: onCancel={() = destroyOnClose 语法错误
 */

// 获取所有需要修复的文件
function getAllTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
            traverse(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`无法读取目录 ${currentDir}:`, error.message);
    }
  }
  
  traverse(dir);
  return files;
}

// 修复单个文件
function fixModalSyntaxInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 检查是否包含需要修复的语法错误
    if (content.includes('onCancel={() = destroyOnClose')) {
      console.log(`正在修复: ${path.relative(process.cwd(), filePath)}`);
      
      // 修复语法错误的正则表达式
      const patterns = [
        // 模式1: 基本语法错误修复
        {
          pattern: /onCancel=\{\(\)\s*=\s*destroyOnClose\s+keyboard=\{true\}\s+maskClosable=\{true\}\>\s*\{/g,
          replacement: 'onCancel={() => {'
        },
        // 模式2: 修复嵌套大括号
        {
          pattern: /try\s*\{\s*\{/g,
          replacement: 'try {'
        },
        // 模式3: 添加缺少的分号
        {
          pattern: /(set\w+\(false\))\s*(?=\s*\})/g,
          replacement: '$1;'
        },
        {
          pattern: /(set\w+\(null\))\s*(?=\s*\})/g,
          replacement: '$1;'
        },
        {
          pattern: /(set\w+\(true\))\s*(?=\s*\})/g,
          replacement: '$1;'
        }
      ];
      
      // 应用所有修复模式
      patterns.forEach(({ pattern, replacement }) => {
        if (pattern.test(content)) {
          content = content.replace(pattern, replacement);
          changed = true;
        }
      });
      
      // 修复属性位置 - 在Modal标签结束前添加属性
      if (changed) {
        // 查找Modal标签的结束位置并添加属性
        const modalTagPattern = /(<Modal[^>]*?)(\s*>)/g;
        content = content.replace(modalTagPattern, (match, openTag, closing) => {
          if (!openTag.includes('destroyOnClose')) {
            return `${openTag}
        destroyOnClose
        keyboard={true}
        maskClosable={true}${closing}`;
          }
          return match;
        });
      }
    }
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🚀 开始批量修复Modal语法错误...\n');
  
  const srcDir = path.join(__dirname, 'src');
  if (!fs.existsSync(srcDir)) {
    console.error('❌ 找不到src目录');
    process.exit(1);
  }
  
  const files = getAllTsxFiles(srcDir);
  console.log(`📁 找到 ${files.length} 个TypeScript/React文件\n`);
  
  let fixedCount = 0;
  let totalErrors = 0;
  
  // 首先统计需要修复的文件数量
  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('onCancel={() = destroyOnClose')) {
        totalErrors++;
      }
    } catch (error) {
      // 忽略读取错误
    }
  }
  
  console.log(`🔍 发现 ${totalErrors} 个文件包含语法错误\n`);
  
  if (totalErrors === 0) {
    console.log('✅ 没有发现需要修复的语法错误！');
    return;
  }
  
  // 修复文件
  for (const file of files) {
    if (fixModalSyntaxInFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 修复完成！`);
  console.log(`📊 统计信息:`);
  console.log(`   - 检查文件数: ${files.length}`);
  console.log(`   - 发现错误数: ${totalErrors}`);
  console.log(`   - 修复文件数: ${fixedCount}`);
  
  if (fixedCount > 0) {
    console.log(`\n📋 修复内容包括:`);
    console.log(`   ✓ 修复 onCancel={() = destroyOnClose 语法错误`);
    console.log(`   ✓ 正确放置 destroyOnClose、keyboard、maskClosable 属性`);
    console.log(`   ✓ 修复嵌套大括号问题`);
    console.log(`   ✓ 添加缺少的分号`);
    console.log(`\n🎉 所有Modal关闭按钮现在应该可以正常工作了！`);
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { fixModalSyntaxInFile };
