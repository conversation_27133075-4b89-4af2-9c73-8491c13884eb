#!/usr/bin/env node

/**
 * 修复Modal组件的语法错误
 * 这个脚本会修复所有包含"onCancel={() = destroyOnClose"语法错误的文件
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归获取所有匹配的文件
function getAllFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过node_modules等目录
        if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
          traverse(fullPath);
        }
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 修复Modal组件的语法错误
function fixModalSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复1: 修复 "onCancel={() = destroyOnClose" 语法错误
    const syntaxErrorPattern = /onCancel=\{\(\)\s*=\s*destroyOnClose\s+keyboard=\{true\}\s+maskClosable=\{true\}\>\s*\{([^}]*)\}/g;
    content = content.replace(syntaxErrorPattern, (match, handler) => {
      changed = true;
      // 清理handler内容，移除多余的大括号
      const cleanHandler = handler.replace(/^\s*\{\s*/, '').replace(/\s*\}\s*$/, '').trim();
      return `onCancel={() => {
          ${cleanHandler}
        }}
        destroyOnClose
        keyboard={true}
        maskClosable={true}`;
    });
    
    // 修复2: 修复更复杂的语法错误模式
    const complexPattern = /onCancel=\{\(\)\s*=\s*destroyOnClose\s+keyboard=\{true\}\s+maskClosable=\{true\}\>\s*\{([^}]*try[^}]*catch[^}]*)\}/gs;
    content = content.replace(complexPattern, (match, handler) => {
      changed = true;
      return `onCancel={() => {
          ${handler.trim()}
        }}
        destroyOnClose
        keyboard={true}
        maskClosable={true}`;
    });
    
    // 修复3: 修复简单的语法错误
    const simplePattern = /onCancel=\{\(\)\s*=\s*destroyOnClose\s+keyboard=\{true\}\s+maskClosable=\{true\}\>\s*\{/g;
    content = content.replace(simplePattern, () => {
      changed = true;
      return `onCancel={() => {`;
    });
    
    // 修复4: 修复遗留的属性位置问题
    const attributePattern = /(\}\s*)\s*destroyOnClose\s+keyboard=\{true\}\s+maskClosable=\{true\}/g;
    content = content.replace(attributePattern, (match, closing) => {
      changed = true;
      return `${closing}
        destroyOnClose
        keyboard={true}
        maskClosable={true}`;
    });
    
    // 修复5: 修复嵌套大括号问题
    const nestedBracePattern = /onCancel=\{\(\)\s*=>\s*\{\s*try\s*\{\s*\{([^}]*)\}/g;
    content = content.replace(nestedBracePattern, (match, handler) => {
      changed = true;
      return `onCancel={() => {
          try {
            ${handler.trim()}`;
    });
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${path.relative(process.cwd(), filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🚀 开始修复Modal语法错误...');
  
  const srcDir = path.join(__dirname, 'src');
  if (!fs.existsSync(srcDir)) {
    console.error('❌ 找不到src目录');
    process.exit(1);
  }
  
  const files = getAllFiles(srcDir);
  console.log(`📁 找到 ${files.length} 个文件`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (fixModalSyntaxErrors(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 修复完成！共修复了 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n📋 修复内容包括:');
    console.log('  - 修复 onCancel={() = destroyOnClose 语法错误');
    console.log('  - 正确放置 destroyOnClose、keyboard、maskClosable 属性');
    console.log('  - 修复嵌套大括号问题');
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { fixModalSyntaxErrors };
