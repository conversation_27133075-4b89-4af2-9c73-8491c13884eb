/**
 * 水体材质预设选择器组件
 */
import React, { useState, useEffect } from 'react';
import { Modal, Button, Card, List, Tabs, Select, Tag, Empty, Spin, Input } from 'antd';
import { InfoCircleOutlined, StarOutlined, StarFilled } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { updateWaterMaterial } from '../../../store/rendering/waterMaterialSlice';

const { TabPane } = Tabs;
const { Option } = Select;
const { Search } = Input;

/**
 * 水体材质预设接口
 */
interface WaterMaterialPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设类别 */
  category: string;
  /** 预设标签 */
  tags: string[];
  /** 预设缩略图 */
  thumbnail?: string;
  /** 预设作者 */
  author?: string;
  /** 水体材质配置 */
  config: any;
}

/**
 * 水体材质预设选择器属性
 */
interface WaterMaterialPresetSelectorProps {
  /** 是否可见 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 选择回调 */
  onSelect: (presetId: string) => void;
  /** 实体ID */
  entityId: string;
}

/**
 * 水体材质预设选择器组件
 */
const WaterMaterialPresetSelector: React.FC<WaterMaterialPresetSelectorProps> = ({ 
  visible, 
  onClose, 
  onSelect,
  entityId 
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 预设列表
  const [presets, setPresets] = useState<WaterMaterialPreset[]>([]);

  // 加载状态
  const [loading, setLoading] = useState(true);

  // 搜索关键字
  const [searchKeyword, setSearchKeyword] = useState('');

  // 选中的预设类别
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 选中的标签
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // 收藏的预设
  const [favoritePresets, setFavoritePresets] = useState<string[]>([]);

  // 所有类别
  const [allCategories, setAllCategories] = useState<string[]>([]);

  // 所有标签
  const [allTags, setAllTags] = useState<string[]>([]);

  // 选中的预设
  const [selectedPreset, setSelectedPreset] = useState<WaterMaterialPreset | null>(null);

  // 从Redux获取水体材质数据（如果需要的话）
  // const waterMaterial = useSelector((state: RootState) =>
  //   state.waterMaterial.waterMaterials.find(wm => wm.entityId === entityId)
  // );

  // 加载预设
  useEffect(() => {
    const loadPresets = async () => {
      setLoading(true);
      try {
        // 这里应该从WaterMaterialPresetManager获取预设
        // 由于我们不能直接在React组件中访问引擎实例，所以这里模拟从服务获取
        // 实际实现应该通过Redux action或服务来获取
        const response = await fetch('/api/water-material-presets');
        const data = await response.json();
        setPresets(data.presets);
        setAllCategories(data.categories);
        setAllTags(data.tags);
      } catch (error) {
        console.error('加载水体材质预设失败:', error);
        // 使用模拟数据
        const mockPresets: WaterMaterialPreset[] = [
          {
            id: 'ocean_deep_blue',
            name: '深蓝海洋',
            description: '深蓝色海洋水体预设，适合开阔海域',
            category: '海洋',
            tags: ['海洋', '蓝色', '波浪'],
            thumbnail: '/assets/thumbnails/water/ocean_deep_blue.jpg',
            author: '系统',
            config: {} // 实际应该有完整的配置
          },
          {
            id: 'lake_clear',
            name: '清澈湖泊',
            description: '清澈的湖泊水体预设，适合平静的湖面',
            category: '湖泊',
            tags: ['湖泊', '清澈', '平静'],
            thumbnail: '/assets/thumbnails/water/lake_clear.jpg',
            author: '系统',
            config: {} // 实际应该有完整的配置
          },
          {
            id: 'river_flowing',
            name: '流动河流',
            description: '流动的河流水体预设，适合有流动感的河流',
            category: '河流',
            tags: ['河流', '流动', '清澈'],
            thumbnail: '/assets/thumbnails/water/river_flowing.jpg',
            author: '系统',
            config: {} // 实际应该有完整的配置
          },
          {
            id: 'underground_lake',
            name: '地下湖泊',
            description: '地下湖泊水体预设，适合洞穴中的湖泊',
            category: '地下水体',
            tags: ['地下', '湖泊', '黑暗'],
            thumbnail: '/assets/thumbnails/water/underground_lake.jpg',
            author: '系统',
            config: {} // 实际应该有完整的配置
          },
          {
            id: 'underground_river',
            name: '地下河流',
            description: '地下河流水体预设，适合洞穴中的河流',
            category: '地下水体',
            tags: ['地下', '河流', '流动'],
            thumbnail: '/assets/thumbnails/water/underground_river.jpg',
            author: '系统',
            config: {} // 实际应该有完整的配置
          }
        ];
        setPresets(mockPresets);
        setAllCategories(['海洋', '湖泊', '河流', '地下水体', '特殊水体']);
        setAllTags(['海洋', '蓝色', '波浪', '湖泊', '清澈', '平静', '河流', '流动', '地下', '黑暗']);
      } finally {
        setLoading(false);
      }
    };

    // 加载收藏的预设
    const loadFavorites = () => {
      const favorites = localStorage.getItem('waterMaterialFavorites');
      if (favorites) {
        setFavoritePresets(JSON.parse(favorites));
      }
    };

    if (visible) {
      loadPresets();
      loadFavorites();
    }
  }, [visible]);

  // 过滤预设
  const filteredPresets = presets.filter(preset => {
    // 根据搜索关键字过滤
    const matchesKeyword = 
      searchKeyword === '' || 
      preset.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      preset.description.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      preset.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()));

    // 根据类别过滤
    const matchesCategory = selectedCategory === 'all' || preset.category === selectedCategory;

    // 根据标签过滤
    const matchesTags = 
      selectedTags.length === 0 || 
      selectedTags.every(tag => preset.tags.includes(tag));

    return matchesKeyword && matchesCategory && matchesTags;
  });

  // 收藏的预设
  const favoritePresetsList = presets.filter(preset => favoritePresets.includes(preset.id));

  // 切换收藏
  const toggleFavorite = (presetId: string) => {
    let newFavorites: string[];
    if (favoritePresets.includes(presetId)) {
      newFavorites = favoritePresets.filter(id => id !== presetId);
    } else {
      newFavorites = [...favoritePresets, presetId];
    }
    setFavoritePresets(newFavorites);
    localStorage.setItem('waterMaterialFavorites', JSON.stringify(newFavorites));
  };

  // 应用预设
  const handleApplyPreset = (presetId: string) => {
    // 根据预设ID获取预设配置
    const preset = presets.find(p => p.id === presetId);
    if (preset) {
      // 应用预设配置到水体材质
      dispatch(updateWaterMaterial({ entityId, properties: preset.config }));
    }
    onSelect(presetId);
  };

  // 渲染预设卡片
  const renderPresetCard = (preset: WaterMaterialPreset) => {
    const isFavorite = favoritePresets.includes(preset.id);

    return (
      <Card
        hoverable
        style={{ width: 240, marginBottom: 16 }}
        cover={preset.thumbnail ? <img alt={preset.name} src={preset.thumbnail} /> : null}
        actions={[
          <Button
            key="select"
            type="primary"
            size="small"
            onClick={() => handleApplyPreset(preset.id)}
          >
            {t('editor.common.apply')}
          </Button>,
          <Button
            key="info"
            type="text"
            size="small"
            icon={<InfoCircleOutlined />}
            onClick={() => setSelectedPreset(preset)}
          />,
          <Button
            key="favorite"
            type="text"
            size="small"
            icon={isFavorite ? <StarFilled /> : <StarOutlined />}
            onClick={() => toggleFavorite(preset.id)}
          />
        ]}
      >
        <Card.Meta
          title={preset.name}
          description={
            <>
              <div className="preset-description">{preset.description}</div>
              <div className="preset-tags">
                {preset.tags.map(tag => (
                  <Tag key={tag} color="blue">{tag}</Tag>
                ))}
              </div>
              <div className="preset-author">{t('editor.common.author')}: {preset.author}</div>
            </>
          }
        />
      </Card>
    );
  };

  // 渲染预设详情
  const renderPresetDetail = () => {
    if (!selectedPreset) return null;

    return (
      <Modal
        title={selectedPreset.name}
        open={!!selectedPreset}
        onCancel={() => {
          try {
            setSelectedPreset(null)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={[
          <Button key="close" onClick={() => setSelectedPreset(null)}>
            {t('editor.common.close')}
          </Button>,
          <Button key="apply" type="primary" onClick={() => handleApplyPreset(selectedPreset.id)}>
            {t('editor.common.apply')}
          </Button>
        ]}
      >
        <div className="preset-detail">
          {selectedPreset.thumbnail && (
            <div className="preset-thumbnail">
              <img src={selectedPreset.thumbnail} alt={selectedPreset.name} />
            </div>
          )}
          <div className="preset-info">
            <h3>{t('editor.common.description')}</h3>
            <p>{selectedPreset.description}</p>
            <h3>{t('editor.common.category')}</h3>
            <p>{selectedPreset.category}</p>
            <h3>{t('editor.common.tags')}</h3>
            <div>
              {selectedPreset.tags.map(tag => (
                <Tag key={tag} color="blue">{tag}</Tag>
              ))}
            </div>
            <h3>{t('editor.common.author')}</h3>
            <p>{selectedPreset.author}</p>
          </div>
        </div>
      </Modal>
    );
  };

  return (
    <>
      <Modal
        title={t('editor.rendering.water.selectWaterPreset')}
        open={visible}
        onCancel={onClose}
        width={800}
        footer={[
          <Button key="close" onClick={onClose}>
            {t('editor.common.close')}
          </Button>
        ]}
      >
        <div className="preset-selector-container">
          <div className="preset-selector-filters">
            <Search
              placeholder={t('editor.common.search') || '搜索'}
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              style={{ marginBottom: 16 }}
            />

            <Select
              placeholder={t('editor.rendering.water.selectCategory')}
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: '100%', marginBottom: 16 }}
            >
              <Option value="all">{t('editor.common.all')}</Option>
              {allCategories.map(category => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>

            <Select
              mode="multiple"
              placeholder={t('editor.rendering.water.selectTags')}
              value={selectedTags}
              onChange={setSelectedTags}
              style={{ width: '100%', marginBottom: 16 }}
            >
              {allTags.map(tag => (
                <Option key={tag} value={tag}>{tag}</Option>
              ))}
            </Select>
          </div>

          <div className="preset-selector-content">
            <Tabs defaultActiveKey="all">
              <TabPane tab={t('editor.common.all')} key="all">
                {loading ? (
                  <div className="preset-loading">
                    <Spin />
                    <div>{t('editor.common.loading')}</div>
                  </div>
                ) : filteredPresets.length > 0 ? (
                  <List
                    grid={{ gutter: 16, column: 3 }}
                    dataSource={filteredPresets}
                    renderItem={renderPresetCard}
                  />
                ) : (
                  <Empty description={t('editor.rendering.water.noPresetsFound')} />
                )}
              </TabPane>
              <TabPane tab={t('editor.common.favorites')} key="favorites">
                {favoritePresetsList.length > 0 ? (
                  <List
                    grid={{ gutter: 16, column: 3 }}
                    dataSource={favoritePresetsList}
                    renderItem={renderPresetCard}
                  />
                ) : (
                  <Empty description={t('editor.rendering.water.noFavorites')} />
                )}
              </TabPane>
            </Tabs>
          </div>
        </div>
      </Modal>
      {renderPresetDetail()}
    </>
  );
};

export default WaterMaterialPresetSelector;
