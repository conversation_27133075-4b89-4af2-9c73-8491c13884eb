/**
 * 地形导入导出面板组件
 * 用于导入和导出地形数据
 */
import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Upload,
  Select,
  Form,
  InputNumber,
  Tooltip,
  message,
  Divider,
  Space,
  Modal,
  Row,
  Col,
  Table,
  Checkbox,
  Switch
} from 'antd';
import {
  UploadOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FileUnknownOutlined,
  EyeOutlined,
  FileZipOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import './TerrainImportExportPanel.less';

// 类型定义
enum HeightMapFormat {
  PNG = 'png',
  JPEG = 'jpeg',
  RAW = 'raw',
  R16 = 'r16',
  R32 = 'r32',
  ASC = 'asc',
  HGT = 'hgt',
  TER = 'ter',
  BT = 'bt'
}

enum ThirdPartyTerrainFormat {
  UNITY = 'unity',
  UNREAL = 'unreal',
  WORLD_MACHINE = 'world_machine',
  TERRAGEN = 'terragen',
  L3DT = 'l3dt',
  GEOTIFF = 'geotiff',
  USGS_DEM = 'usgs_dem',
  SRTM = 'srtm'
}

enum CoordinateSystem {
  LEFT_HANDED = 'left_handed',
  RIGHT_HANDED = 'right_handed'
}

interface BatchImportResult {
  fileName: string;
  success: boolean;
  error?: string;
}

interface BatchExportResult {
  fileName: string;
  success: boolean;
  error?: string;
  data?: Blob | ArrayBuffer | string;
}

// 模拟的预览服务类
class TerrainPreviewService {
  private domElement: HTMLCanvasElement;

  constructor(options: any) {
    this.domElement = document.createElement('canvas');
    this.domElement.width = options.width || 800;
    this.domElement.height = options.height || 600;
    this.domElement.style.backgroundColor = '#222222';
  }

  getDomElement(): HTMLCanvasElement {
    return this.domElement;
  }

  resize(width: number, height: number): void {
    this.domElement.width = width;
    this.domElement.height = height;
  }

  async previewHeightMap(file: File, options: any): Promise<boolean> {
    try {
      // 创建图像元素来预览高度图
      const img = new Image();
      const canvas = this.domElement;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        console.error('无法获取Canvas上下文');
        return false;
      }

      return new Promise((resolve) => {
        img.onload = () => {
          // 设置画布大小
          canvas.width = Math.min(img.width, 512);
          canvas.height = Math.min(img.height, 512);

          // 绘制图像
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // 如果需要，可以添加高度图分析
          console.log('预览高度图:', file.name, `${img.width}x${img.height}`, options);
          resolve(true);
        };

        img.onerror = () => {
          console.error('加载高度图失败:', file.name);
          resolve(false);
        };

        // 创建对象URL并加载图像
        const url = URL.createObjectURL(file);
        const originalOnLoad = img.onload;

        img.onload = () => {
          URL.revokeObjectURL(url);
          if (originalOnLoad) {
            originalOnLoad.call(img, new Event('load'));
          }
        };

        img.src = url;
      });
    } catch (error) {
      console.error('预览高度图失败:', error);
      return false;
    }
  }

  previewJSON(jsonText: string, _options: any): boolean {
    try {
      // 解析JSON数据
      const data = JSON.parse(jsonText);

      // 验证JSON结构
      if (!data || typeof data !== 'object') {
        console.error('无效的JSON数据结构');
        return false;
      }

      // 检查是否包含地形数据
      const hasHeightData = data.heightData || data.terrain?.heightData;
      const hasMetadata = data.width && data.height;

      if (!hasHeightData && !hasMetadata) {
        console.error('JSON中缺少地形数据');
        return false;
      }

      // 在画布上绘制简单的预览
      const canvas = this.domElement;
      const ctx = canvas.getContext('2d');

      if (ctx && hasHeightData) {
        const heightData = data.heightData || data.terrain.heightData;
        const width = data.width || data.terrain?.width || 256;
        const height = data.height || data.terrain?.height || 256;

        canvas.width = Math.min(width, 512);
        canvas.height = Math.min(height, 512);

        // 创建简单的高度图可视化
        const imageData = ctx.createImageData(canvas.width, canvas.height);

        for (let i = 0; i < heightData.length && i < imageData.data.length / 4; i++) {
          const value = Math.floor(heightData[i] * 255);
          const index = i * 4;
          imageData.data[index] = value;     // R
          imageData.data[index + 1] = value; // G
          imageData.data[index + 2] = value; // B
          imageData.data[index + 3] = 255;   // A
        }

        ctx.putImageData(imageData, 0, 0);
      }

      console.log('预览JSON:', `${jsonText.length} 字符`, {
        hasHeightData,
        hasMetadata,
        width: data.width || data.terrain?.width,
        height: data.height || data.terrain?.height
      });

      return true;
    } catch (error) {
      console.error('预览JSON失败:', error);
      return false;
    }
  }

  async previewThirdPartyFormat(format: ThirdPartyTerrainFormat, data: ArrayBuffer, options: any): Promise<boolean> {
    try {
      // 根据格式进行基本的数据验证
      let isValid = false;

      switch (format) {
        case ThirdPartyTerrainFormat.UNITY:
          // Unity地形文件通常是二进制格式
          isValid = data.byteLength > 0 && data.byteLength % 4 === 0;
          break;

        case ThirdPartyTerrainFormat.UNREAL:
          // Unreal地形文件格式验证
          isValid = data.byteLength > 0;
          break;

        case ThirdPartyTerrainFormat.GEOTIFF:
          // GeoTIFF文件头验证
          const view = new DataView(data);
          const magic = view.getUint16(0, true);
          isValid = magic === 0x4949 || magic === 0x4D4D; // TIFF magic numbers
          break;

        default:
          // 其他格式的基本验证
          isValid = data.byteLength > 0;
      }

      if (!isValid) {
        console.error('无效的第三方格式数据:', format);
        return false;
      }

      // 在画布上显示格式信息
      const canvas = this.domElement;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        canvas.width = 400;
        canvas.height = 300;

        // 清空画布
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制格式信息
        ctx.fillStyle = '#333';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';

        ctx.fillText(`格式: ${format.toUpperCase()}`, canvas.width / 2, 50);
        ctx.fillText(`大小: ${(data.byteLength / 1024).toFixed(2)} KB`, canvas.width / 2, 80);
        ctx.fillText('预览功能开发中...', canvas.width / 2, 150);

        // 绘制边框
        ctx.strokeStyle = '#ccc';
        ctx.strokeRect(0, 0, canvas.width, canvas.height);
      }

      console.log('预览第三方格式:', format, `${data.byteLength} 字节`, options);
      return true;
    } catch (error) {
      console.error('预览第三方格式失败:', error);
      return false;
    }
  }

  dispose(): void {
    // 清理资源
    if (this.domElement.parentNode) {
      this.domElement.parentNode.removeChild(this.domElement);
    }
  }
}

const { Option } = Select;
const { Dragger } = Upload;

/**
 * 地形导入导出面板属性
 */
interface TerrainImportExportPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 地形修改回调 */
  onTerrainModified?: () => void;
}

/**
 * 地形导入导出面板组件
 */
const TerrainImportExportPanel: React.FC<TerrainImportExportPanelProps> = ({
  entityId,
  editable = true,
  onTerrainModified
}) => {
  const { t } = useTranslation();

  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) => {
    if (!entityId) return null;
    const entity = state.scene.entities.find(entity => entity.id === entityId);
    return entity?.components?.TerrainComponent || null;
  });

  // 状态
  const [activeTab, setActiveTab] = useState('heightmap');
  const [heightMapFormat, setHeightMapFormat] = useState<HeightMapFormat>(HeightMapFormat.PNG);
  const [thirdPartyFormat, setThirdPartyFormat] = useState<ThirdPartyTerrainFormat>(ThirdPartyTerrainFormat.UNITY);
  const [exportOptions, setExportOptions] = useState<{[key: string]: any}>({
    includeTextures: true,
    includeNormals: true,
    includePhysics: true,
    prettyPrint: true,
    flipY: false,
    normalize: true,
    width: 0,
    height: 0,
    heightScale: 1.0,
    heightOffset: 0.0,
    sourceCoordinateSystem: CoordinateSystem.LEFT_HANDED,
    targetCoordinateSystem: CoordinateSystem.LEFT_HANDED,
    flipX: false,
    flipZ: false,
    useWireframe: false
  });

  // 预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<File | null>(null);
  const [previewType, setPreviewType] = useState<'heightmap' | 'json' | 'thirdparty'>('heightmap');
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const previewServiceRef = useRef<TerrainPreviewService | null>(null);

  // 批量导入/导出状态
  const [batchImportVisible, setBatchImportVisible] = useState(false);
  const [batchExportVisible, setBatchExportVisible] = useState(false);
  const [batchFiles, setBatchFiles] = useState<File[]>([]);
  const [batchResults, setBatchResults] = useState<BatchImportResult[]>([]);
  const [batchExportResults, setBatchExportResults] = useState<BatchExportResult[]>([]);
  const [batchExportFormats, setBatchExportFormats] = useState<string[]>([]);
  const [batchExportOptions, setBatchExportOptions] = useState<any[]>([]);

  // 处理标签页变更
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 处理高度图格式变更
  const handleHeightMapFormatChange = (value: HeightMapFormat) => {
    setHeightMapFormat(value);
  };

  // 处理第三方格式变更
  const handleThirdPartyFormatChange = (value: ThirdPartyTerrainFormat) => {
    setThirdPartyFormat(value);
  };

  // 处理导出选项变更
  const handleExportOptionChange = (name: string, value: any) => {
    setExportOptions({
      ...exportOptions,
      [name]: value
    });
  };

  // 初始化预览服务
  useEffect(() => {
    return () => {
      // 组件卸载时销毁预览服务
      if (previewServiceRef.current) {
        previewServiceRef.current.dispose();
        previewServiceRef.current = null;
      }
    };
  }, []);

  // 处理预览窗口大小变化
  useEffect(() => {
    if (previewVisible && previewContainerRef.current) {
      const container = previewContainerRef.current;
      const width = container.clientWidth;
      const height = container.clientHeight;

      if (!previewServiceRef.current) {
        previewServiceRef.current = new TerrainPreviewService({
          width,
          height,
          backgroundColor: 0x222222,
          useAO: true,
          useShadows: true,
          useWireframe: false,
          useGrid: true
        });

        container.appendChild(previewServiceRef.current.getDomElement());
      } else {
        previewServiceRef.current.resize(width, height);
      }

      // 如果有预览文件，显示预览
      if (previewFile) {
        showPreview();
      }
    }
  }, [previewVisible]);

  // 显示预览
  const showPreview = async () => {
    if (!previewServiceRef.current || !previewFile) {
      return;
    }

    let success = false;

    switch (previewType) {
      case 'heightmap':
        success = await previewServiceRef.current.previewHeightMap(previewFile, {
          flipY: exportOptions.flipY,
          heightScale: exportOptions.heightScale,
          heightOffset: exportOptions.heightOffset,
          applySmoothing: false
        });
        break;
      case 'json':
        const jsonText = await readFileAsText(previewFile);
        success = previewServiceRef.current.previewJSON(jsonText, {
          computeNormals: true,
          applySmoothing: false
        });
        break;
      case 'thirdparty':
        const data = await readFileAsArrayBuffer(previewFile);
        success = await previewServiceRef.current.previewThirdPartyFormat(
          thirdPartyFormat,
          data,
          {
            computeNormals: true,
            applySmoothing: false,
            heightScale: exportOptions.heightScale,
            heightOffset: exportOptions.heightOffset,
            sourceCoordinateSystem: exportOptions.sourceCoordinateSystem,
            targetCoordinateSystem: exportOptions.targetCoordinateSystem,
            flipX: exportOptions.flipX,
            flipY: exportOptions.flipY,
            flipZ: exportOptions.flipZ
          }
        );
        break;
    }

    if (!success) {
      message.error(t('terrain.preview.previewFailed'));
    }
  };

  // 处理预览按钮点击
  const handlePreview = (file: File, type: 'heightmap' | 'json' | 'thirdparty') => {
    setPreviewFile(file);
    setPreviewType(type);
    setPreviewVisible(true);
  };

  // 读取文件为文本
  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = (e) => {
        reject(e);
      };
      reader.readAsText(file);
    });
  };

  // 读取文件为ArrayBuffer
  const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as ArrayBuffer);
      };
      reader.onerror = (e) => {
        reject(e);
      };
      reader.readAsArrayBuffer(file);
    });
  };

  // 第一个isTextFormat函数已移除，使用下面的版本

  // 获取格式选项
  const getFormatOptions = () => {
    switch (activeTab) {
      case 'heightmap':
        return [
          { label: t('terrain.importExport.formats.png'), value: HeightMapFormat.PNG },
          { label: t('terrain.importExport.formats.jpeg'), value: HeightMapFormat.JPEG },
          { label: t('terrain.importExport.formats.raw'), value: HeightMapFormat.RAW },
          { label: t('terrain.importExport.formats.r16'), value: HeightMapFormat.R16 },
          { label: t('terrain.importExport.formats.r32'), value: HeightMapFormat.R32 },
          { label: t('terrain.importExport.formats.asc'), value: HeightMapFormat.ASC },
          { label: t('terrain.importExport.formats.hgt'), value: HeightMapFormat.HGT },
          { label: t('terrain.importExport.formats.ter'), value: HeightMapFormat.TER },
          { label: t('terrain.importExport.formats.bt'), value: HeightMapFormat.BT }
        ];
      case 'thirdparty':
        return [
          { label: t('terrain.importExport.formats.unity'), value: ThirdPartyTerrainFormat.UNITY },
          { label: t('terrain.importExport.formats.unreal'), value: ThirdPartyTerrainFormat.UNREAL },
          { label: t('terrain.importExport.formats.worldMachine'), value: ThirdPartyTerrainFormat.WORLD_MACHINE },
          { label: t('terrain.importExport.formats.terragen'), value: ThirdPartyTerrainFormat.TERRAGEN },
          { label: t('terrain.importExport.formats.l3dt'), value: ThirdPartyTerrainFormat.L3DT },
          { label: t('terrain.importExport.formats.geotiff'), value: ThirdPartyTerrainFormat.GEOTIFF },
          { label: t('terrain.importExport.formats.usgsDem'), value: ThirdPartyTerrainFormat.USGS_DEM },
          { label: t('terrain.importExport.formats.srtm'), value: ThirdPartyTerrainFormat.SRTM }
        ];
      default:
        return [];
    }
  };

  // 获取导出选项
  const getExportOptions = () => {
    switch (activeTab) {
      case 'heightmap':
        return [
          { key: 'flipY', label: t('terrain.importExport.flipY') },
          { key: 'normalize', label: t('terrain.importExport.normalize') }
        ];
      case 'json':
      case 'thirdparty':
        return [
          { key: 'includeTextures', label: t('terrain.importExport.includeTextures') },
          { key: 'includeNormals', label: t('terrain.importExport.includeNormals') },
          { key: 'includePhysics', label: t('terrain.importExport.includePhysics') }
        ];
      default:
        return [];
    }
  };

  // 处理导出高度图
  const handleExportHeightMap = async () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 获取宽度和高度
      const width = exportOptions.width > 0 ? exportOptions.width : terrainData.resolution;
      const height = exportOptions.height > 0 ? exportOptions.height : terrainData.resolution;

      // 导出高度图
      const blob = await terrainData.exportToHeightMap({
        format: heightMapFormat,
        width,
        height,
        flipY: exportOptions.flipY,
        normalize: exportOptions.normalize
      });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `terrain_heightmap_${Date.now()}.${heightMapFormat.toLowerCase()}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(t('terrain.generation.heightMapExported'));
    } catch (error) {
      console.error('导出高度图失败:', error);
      message.error(t('terrain.errors.exportFailed'));
    }
  };

  // 处理导出JSON
  const handleExportJSON = () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 导出JSON
      const json = terrainData.exportToJSON({
        includeTextures: exportOptions.includeTextures,
        includeNormals: exportOptions.includeNormals,
        includePhysics: exportOptions.includePhysics,
        prettyPrint: exportOptions.prettyPrint
      });

      // 创建Blob
      const blob = new Blob([json], { type: 'application/json' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `terrain_data_${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(t('terrain.generation.jsonExported'));
    } catch (error) {
      console.error('导出JSON失败:', error);
      message.error(t('terrain.errors.exportFailed'));
    }
  };

  // 处理导出第三方格式
  const handleExportThirdParty = async () => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    try {
      // 导出第三方格式
      const data = await terrainData.exportToThirdPartyFormat(thirdPartyFormat, {
        includeTextures: exportOptions.includeTextures,
        includeNormals: exportOptions.includeNormals,
        includePhysics: exportOptions.includePhysics
      });

      // 创建Blob
      const blob = new Blob([data instanceof ArrayBuffer ? data : data], {
        type: data instanceof ArrayBuffer ? 'application/octet-stream' : 'text/plain'
      });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `terrain_${thirdPartyFormat.toLowerCase()}_${Date.now()}.${getFileExtension(thirdPartyFormat)}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(t('terrain.importExport.exportSuccess'));
    } catch (error) {
      console.error('导出第三方格式失败:', error);
      message.error(t('terrain.errors.exportFailed'));
    }
  };

  // 处理导入高度图
  const handleImportHeightMap = async (file: File) => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return false;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.generation.importing'), 0);

      // 导入高度图
      const success = await terrainData.importFromHeightMap(file, {
        flipY: exportOptions.flipY,
        heightScale: 1.0,
        applySmoothing: false
      });

      // 关闭加载中
      message.destroy();

      if (success) {
        message.success(t('terrain.generation.heightMapImported'));
        // 通知地形修改
        if (onTerrainModified) {
          onTerrainModified();
        }
      } else {
        message.error(t('terrain.errors.importFailed'));
      }
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('导入高度图失败:', error);
      message.error(t('terrain.errors.importFailed'));
    }

    return false;
  };

  // 处理导入JSON
  const handleImportJSON = async (file: File) => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return false;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.generation.importing'), 0);

      // 读取文件
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const json = e.target?.result as string;

          // 导入JSON
          const success = terrainData.importFromJSON(json, {
            keepTextures: false,
            keepPhysics: false,
            computeNormals: true,
            applySmoothing: false
          });

          // 关闭加载中
          message.destroy();

          if (success) {
            message.success(t('terrain.generation.jsonImported'));
            // 通知地形修改
            if (onTerrainModified) {
              onTerrainModified();
            }
          } else {
            message.error(t('terrain.errors.importFailed'));
          }
        } catch (error) {
          // 关闭加载中
          message.destroy();
          console.error('导入JSON失败:', error);
          message.error(t('terrain.errors.importFailed'));
        }
      };

      reader.onerror = () => {
        // 关闭加载中
        message.destroy();
        message.error(t('terrain.errors.importFailed'));
      };

      reader.readAsText(file);
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('导入JSON失败:', error);
      message.error(t('terrain.errors.importFailed'));
    }

    return false;
  };

  // 处理导入第三方格式
  const handleImportThirdParty = async (file: File) => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return false;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.generation.importing'), 0);

      // 读取文件
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = e.target?.result;

          // 导入第三方格式
          const success = await terrainData.importFromThirdPartyFormat(
            thirdPartyFormat,
            data as ArrayBuffer | string,
            {
              keepTextures: false,
              keepPhysics: false,
              computeNormals: true,
              applySmoothing: false
            }
          );

          // 关闭加载中
          message.destroy();

          if (success) {
            message.success(t('terrain.importExport.importSuccess'));
            // 通知地形修改
            if (onTerrainModified) {
              onTerrainModified();
            }
          } else {
            message.error(t('terrain.errors.importFailed'));
          }
        } catch (error) {
          // 关闭加载中
          message.destroy();
          console.error('导入第三方格式失败:', error);
          message.error(t('terrain.errors.importFailed'));
        }
      };

      reader.onerror = () => {
        // 关闭加载中
        message.destroy();
        message.error(t('terrain.errors.importFailed'));
      };

      // 根据格式选择读取方式
      if (isTextFormat(thirdPartyFormat)) {
        reader.readAsText(file);
      } else {
        reader.readAsArrayBuffer(file);
      }
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('导入第三方格式失败:', error);
      message.error(t('terrain.errors.importFailed'));
    }

    return false;
  };

  // 获取文件扩展名
  const getFileExtension = (format: ThirdPartyTerrainFormat): string => {
    switch (format) {
      case ThirdPartyTerrainFormat.UNITY:
        return 'raw';
      case ThirdPartyTerrainFormat.UNREAL:
        return 'r16';
      case ThirdPartyTerrainFormat.WORLD_MACHINE:
        return 'ter';
      case ThirdPartyTerrainFormat.TERRAGEN:
        return 'ter';
      case ThirdPartyTerrainFormat.L3DT:
        return 'hfz';
      case ThirdPartyTerrainFormat.GEOTIFF:
        return 'tif';
      case ThirdPartyTerrainFormat.USGS_DEM:
        return 'dem';
      case ThirdPartyTerrainFormat.SRTM:
        return 'hgt';
      default:
        return 'dat';
    }
  };

  // 判断是否为文本格式
  const isTextFormat = (format: ThirdPartyTerrainFormat): boolean => {
    return [
      ThirdPartyTerrainFormat.TERRAGEN,
      ThirdPartyTerrainFormat.WORLD_MACHINE
    ].includes(format);
  };

  // 处理批量导入
  const handleBatchImport = async (type: 'heightmap' | 'json' | 'thirdparty') => {
    if (!entityId || !terrainData || batchFiles.length === 0) {
      return;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.batch.importing'), 0);

      let results: BatchImportResult[] = [];

      switch (type) {
        case 'heightmap':
          results = await terrainData.batchImportHeightMaps(batchFiles, {
            flipY: exportOptions.flipY,
            heightScale: exportOptions.heightScale,
            heightOffset: exportOptions.heightOffset,
            applySmoothing: false
          });
          break;
        case 'json':
          results = await terrainData.batchImportJSON(batchFiles, {
            keepTextures: false,
            keepPhysics: false,
            computeNormals: true,
            applySmoothing: false
          });
          break;
        case 'thirdparty':
          results = await terrainData.batchImportThirdPartyFormat(batchFiles, thirdPartyFormat, {
            keepTextures: false,
            keepPhysics: false,
            computeNormals: true,
            applySmoothing: false,
            heightScale: exportOptions.heightScale,
            heightOffset: exportOptions.heightOffset,
            sourceCoordinateSystem: exportOptions.sourceCoordinateSystem,
            targetCoordinateSystem: exportOptions.targetCoordinateSystem,
            flipX: exportOptions.flipX,
            flipY: exportOptions.flipY,
            flipZ: exportOptions.flipZ
          });
          break;
      }

      // 关闭加载中
      message.destroy();

      // 设置结果
      setBatchResults(results);

      // 通知地形修改
      if (results.some(r => r.success) && onTerrainModified) {
        onTerrainModified();
      }

      // 显示成功消息
      const successCount = results.filter(r => r.success).length;
      if (successCount > 0) {
        message.success(t('terrain.batch.importSuccess', { count: successCount }));
      } else {
        message.error(t('terrain.batch.importFailed'));
      }
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('批量导入失败:', error);
      message.error(t('terrain.errors.importFailed'));
    }
  };

  // 处理批量导出
  const handleBatchExport = async (type: 'heightmap' | 'json' | 'thirdparty') => {
    if (!entityId || !terrainData || batchExportFormats.length === 0) {
      return;
    }

    try {
      // 显示加载中
      message.loading(t('terrain.batch.exporting'), 0);

      let results: BatchExportResult[] = [];

      switch (type) {
        case 'heightmap':
          const heightMapOptions = batchExportFormats.map(format => ({
            format: format as HeightMapFormat,
            width: exportOptions.width > 0 ? exportOptions.width : terrainData.resolution,
            height: exportOptions.height > 0 ? exportOptions.height : terrainData.resolution,
            flipY: exportOptions.flipY,
            normalize: exportOptions.normalize
          }));

          results = await terrainData.batchExportHeightMaps(heightMapOptions);
          break;
        case 'json':
          const jsonOptions = batchExportOptions.map(opt => ({
            includeTextures: opt.includeTextures || exportOptions.includeTextures,
            includeNormals: opt.includeNormals || exportOptions.includeNormals,
            includePhysics: opt.includePhysics || exportOptions.includePhysics,
            prettyPrint: opt.prettyPrint || exportOptions.prettyPrint
          }));

          results = terrainData.batchExportJSON(jsonOptions);
          break;
        case 'thirdparty':
          const thirdPartyOptions = batchExportFormats.map(format => ({
            format: format as ThirdPartyTerrainFormat,
            options: {
              includeTextures: exportOptions.includeTextures,
              includeNormals: exportOptions.includeNormals,
              includePhysics: exportOptions.includePhysics,
              heightScale: exportOptions.heightScale,
              heightOffset: exportOptions.heightOffset,
              sourceCoordinateSystem: exportOptions.sourceCoordinateSystem,
              targetCoordinateSystem: exportOptions.targetCoordinateSystem,
              flipX: exportOptions.flipX,
              flipY: exportOptions.flipY,
              flipZ: exportOptions.flipZ
            }
          }));

          results = await terrainData.batchExportThirdPartyFormat(thirdPartyOptions);
          break;
      }

      // 关闭加载中
      message.destroy();

      // 设置结果
      setBatchExportResults(results);

      // 显示成功消息
      const successCount = results.filter(r => r.success).length;
      if (successCount > 0) {
        message.success(t('terrain.batch.exportSuccess', { count: successCount }));

        // 下载文件
        results.forEach(result => {
          if (result.success && result.data) {
            const blob = result.data instanceof Blob ? result.data : new Blob([result.data], { type: 'application/octet-stream' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = result.fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }
        });
      } else {
        message.error(t('terrain.batch.exportFailed'));
      }
    } catch (error) {
      // 关闭加载中
      message.destroy();
      console.error('批量导出失败:', error);
      message.error(t('terrain.errors.exportFailed'));
    }
  };

  return (
    <div className="terrain-import-export-panel">
      <Card title={t('terrain.importExport.title')}>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              key: 'heightmap',
              label: t('terrain.importExport.heightMap'),
              children: (
            <Form layout="vertical">
              <Form.Item label={t('terrain.importExport.format')}>
                <Select
                  value={heightMapFormat}
                  onChange={handleHeightMapFormatChange}
                  disabled={!editable}
                >
                  <Option value={HeightMapFormat.PNG}>{t('terrain.importExport.formats.png')}</Option>
                  <Option value={HeightMapFormat.JPEG}>{t('terrain.importExport.formats.jpeg')}</Option>
                  <Option value={HeightMapFormat.RAW}>{t('terrain.importExport.formats.raw')}</Option>
                  <Option value={HeightMapFormat.R16}>{t('terrain.importExport.formats.r16')}</Option>
                  <Option value={HeightMapFormat.R32}>{t('terrain.importExport.formats.r32')}</Option>
                  <Option value={HeightMapFormat.ASC}>{t('terrain.importExport.formats.asc')}</Option>
                  <Option value={HeightMapFormat.HGT}>{t('terrain.importExport.formats.hgt')}</Option>
                  <Option value={HeightMapFormat.TER}>{t('terrain.importExport.formats.ter')}</Option>
                  <Option value={HeightMapFormat.BT}>{t('terrain.importExport.formats.bt')}</Option>
                </Select>
              </Form.Item>

              <Form.Item label={t('terrain.importExport.options')}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Form.Item label={t('terrain.importExport.flipY')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.flipY}
                      onChange={(checked) => handleExportOptionChange('flipY', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item label={t('terrain.importExport.normalize')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.normalize}
                      onChange={(checked) => handleExportOptionChange('normalize', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.importExport.width')}
                        <Tooltip title={t('terrain.importExport.widthTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <InputNumber
                      value={exportOptions.width}
                      onChange={(value) => handleExportOptionChange('width', value)}
                      disabled={!editable}
                      min={0}
                      max={4096}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <span>
                        {t('terrain.importExport.height')}
                        <Tooltip title={t('terrain.importExport.heightTooltip')}>
                          <InfoCircleOutlined style={{ marginLeft: 8 }} />
                        </Tooltip>
                      </span>
                    }
                  >
                    <InputNumber
                      value={exportOptions.height}
                      onChange={(value) => handleExportOptionChange('height', value)}
                      disabled={!editable}
                      min={0}
                      max={4096}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Space>
              </Form.Item>

              <Divider />

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={handleExportHeightMap}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.importExport.export')}
                  </Button>

                  <Upload
                    accept=".png,.jpg,.jpeg,.raw,.r16,.r32,.asc,.hgt,.ter,.bt"
                    showUploadList={false}
                    beforeUpload={handleImportHeightMap}
                    disabled={!editable || !terrainData}
                  >
                    <Button icon={<UploadOutlined />} disabled={!editable || !terrainData}>
                      {t('terrain.importExport.import')}
                    </Button>
                  </Upload>

                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => {
                      // 打开文件选择器
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = '.png,.jpg,.jpeg,.raw,.r16,.r32,.asc,.hgt,.ter,.bt';
                      input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) {
                          handlePreview(file, 'heightmap');
                        }
                      };
                      input.click();
                    }}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.preview.preview')}
                  </Button>

                  <Button
                    icon={<FileZipOutlined />}
                    onClick={() => setBatchImportVisible(true)}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.batch.batchImport')}
                  </Button>

                  <Button
                    icon={<FileZipOutlined />}
                    onClick={() => {
                      setBatchExportFormats([HeightMapFormat.PNG, HeightMapFormat.JPEG]);
                      setBatchExportVisible(true);
                    }}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.batch.batchExport')}
                  </Button>
                </Space>
              </Form.Item>

              <Form.Item>
                <Dragger
                  accept=".png,.jpg,.jpeg,.raw,.r16,.r32,.asc,.hgt,.ter,.bt"
                  showUploadList={false}
                  beforeUpload={handleImportHeightMap}
                  disabled={!editable || !terrainData}
                >
                  <p className="ant-upload-drag-icon">
                    <FileImageOutlined />
                  </p>
                  <p className="ant-upload-text">{t('terrain.importExport.dragHeightMap')}</p>
                  <p className="ant-upload-hint">{t('terrain.importExport.heightMapHint')}</p>
                </Dragger>
              </Form.Item>
            </Form>
              )
            },
            {
              key: 'json',
              label: t('terrain.importExport.json'),
              children: (
            <Form layout="vertical">
              <Form.Item label={t('terrain.importExport.options')}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Form.Item label={t('terrain.importExport.includeTextures')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.includeTextures}
                      onChange={(checked) => handleExportOptionChange('includeTextures', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item label={t('terrain.importExport.includeNormals')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.includeNormals}
                      onChange={(checked) => handleExportOptionChange('includeNormals', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item label={t('terrain.importExport.includePhysics')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.includePhysics}
                      onChange={(checked) => handleExportOptionChange('includePhysics', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item label={t('terrain.importExport.prettyPrint')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.prettyPrint}
                      onChange={(checked) => handleExportOptionChange('prettyPrint', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>
                </Space>
              </Form.Item>

              <Divider />

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={handleExportJSON}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.importExport.export')}
                  </Button>

                  <Upload
                    accept=".json"
                    showUploadList={false}
                    beforeUpload={handleImportJSON}
                    disabled={!editable || !terrainData}
                  >
                    <Button icon={<UploadOutlined />} disabled={!editable || !terrainData}>
                      {t('terrain.importExport.import')}
                    </Button>
                  </Upload>

                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => {
                      // 打开文件选择器
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = '.json';
                      input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) {
                          handlePreview(file, 'json');
                        }
                      };
                      input.click();
                    }}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.preview.preview')}
                  </Button>

                  <Button
                    icon={<FileZipOutlined />}
                    onClick={() => setBatchImportVisible(true)}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.batch.batchImport')}
                  </Button>

                  <Button
                    icon={<FileZipOutlined />}
                    onClick={() => {
                      setBatchExportOptions([{
                        includeTextures: true,
                        includeNormals: true,
                        includePhysics: true,
                        prettyPrint: true
                      }]);
                      setBatchExportVisible(true);
                    }}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.batch.batchExport')}
                  </Button>
                </Space>
              </Form.Item>

              <Form.Item>
                <Dragger
                  accept=".json"
                  showUploadList={false}
                  beforeUpload={handleImportJSON}
                  disabled={!editable || !terrainData}
                >
                  <p className="ant-upload-drag-icon">
                    <FileTextOutlined />
                  </p>
                  <p className="ant-upload-text">{t('terrain.importExport.dragJSON')}</p>
                  <p className="ant-upload-hint">{t('terrain.importExport.jsonHint')}</p>
                </Dragger>
              </Form.Item>
            </Form>
              )
            },
            {
              key: 'thirdparty',
              label: t('terrain.importExport.thirdParty'),
              children: (
            <Form layout="vertical">
              <Form.Item label={t('terrain.importExport.format')}>
                <Select
                  value={thirdPartyFormat}
                  onChange={handleThirdPartyFormatChange}
                  disabled={!editable}
                >
                  <Option value={ThirdPartyTerrainFormat.UNITY}>{t('terrain.importExport.formats.unity')}</Option>
                  <Option value={ThirdPartyTerrainFormat.UNREAL}>{t('terrain.importExport.formats.unreal')}</Option>
                  <Option value={ThirdPartyTerrainFormat.WORLD_MACHINE}>{t('terrain.importExport.formats.worldMachine')}</Option>
                  <Option value={ThirdPartyTerrainFormat.TERRAGEN}>{t('terrain.importExport.formats.terragen')}</Option>
                  <Option value={ThirdPartyTerrainFormat.L3DT}>{t('terrain.importExport.formats.l3dt')}</Option>
                  <Option value={ThirdPartyTerrainFormat.GEOTIFF}>{t('terrain.importExport.formats.geotiff')}</Option>
                  <Option value={ThirdPartyTerrainFormat.USGS_DEM}>{t('terrain.importExport.formats.usgsDem')}</Option>
                  <Option value={ThirdPartyTerrainFormat.SRTM}>{t('terrain.importExport.formats.srtm')}</Option>
                </Select>
              </Form.Item>

              <Form.Item label={t('terrain.importExport.options')}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Form.Item label={t('terrain.importExport.includeTextures')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.includeTextures}
                      onChange={(checked) => handleExportOptionChange('includeTextures', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item label={t('terrain.importExport.includeNormals')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.includeNormals}
                      onChange={(checked) => handleExportOptionChange('includeNormals', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item label={t('terrain.importExport.includePhysics')} valuePropName="checked">
                    <Switch
                      checked={exportOptions.includePhysics}
                      onChange={(checked) => handleExportOptionChange('includePhysics', checked)}
                      disabled={!editable}
                    />
                  </Form.Item>
                </Space>
              </Form.Item>

              <Divider />

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={handleExportThirdParty}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.importExport.export')}
                  </Button>

                  <Upload
                    showUploadList={false}
                    beforeUpload={handleImportThirdParty}
                    disabled={!editable || !terrainData}
                  >
                    <Button icon={<UploadOutlined />} disabled={!editable || !terrainData}>
                      {t('terrain.importExport.import')}
                    </Button>
                  </Upload>

                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => {
                      // 打开文件选择器
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) {
                          handlePreview(file, 'thirdparty');
                        }
                      };
                      input.click();
                    }}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.preview.preview')}
                  </Button>

                  <Button
                    icon={<FileZipOutlined />}
                    onClick={() => setBatchImportVisible(true)}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.batch.batchImport')}
                  </Button>

                  <Button
                    icon={<FileZipOutlined />}
                    onClick={() => {
                      setBatchExportFormats([
                        ThirdPartyTerrainFormat.UNITY,
                        ThirdPartyTerrainFormat.UNREAL
                      ]);
                      setBatchExportVisible(true);
                    }}
                    disabled={!editable || !terrainData}
                  >
                    {t('terrain.batch.batchExport')}
                  </Button>
                </Space>
              </Form.Item>

              <Form.Item>
                <Dragger
                  showUploadList={false}
                  beforeUpload={handleImportThirdParty}
                  disabled={!editable || !terrainData}
                >
                  <p className="ant-upload-drag-icon">
                    <FileUnknownOutlined />
                  </p>
                  <p className="ant-upload-text">{t('terrain.importExport.dragThirdParty')}</p>
                  <p className="ant-upload-hint">{t('terrain.importExport.thirdPartyHint')}</p>
                </Dragger>
              </Form.Item>
            </Form>
              )
            }
          ]}
        />
      </Card>

      {/* 预览模态框 */}
      <Modal
        title={t('terrain.preview.title')}
        open={previewVisible}
        onCancel={() => {
          try {
            setPreviewVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
      >
        <div
          ref={previewContainerRef}
          style={{ width: '100%', height: '500px', backgroundColor: '#222' }}
        />

        <Divider />

        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label={t('terrain.preview.heightScale')}>
                <InputNumber
                  value={exportOptions.heightScale}
                  onChange={(value) => handleExportOptionChange('heightScale', value)}
                  min={0.1}
                  max={10}
                  step={0.1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label={t('terrain.preview.heightOffset')}>
                <InputNumber
                  value={exportOptions.heightOffset}
                  onChange={(value) => handleExportOptionChange('heightOffset', value)}
                  min={-1}
                  max={1}
                  step={0.1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label={t('terrain.preview.wireframe')}>
                <Switch
                  checked={exportOptions.useWireframe}
                  onChange={(checked) => {
                    handleExportOptionChange('useWireframe', checked);
                    if (previewServiceRef.current) {
                      // 更新预览
                      showPreview();
                    }
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 批量导入模态框 */}
      <Modal
        title={t('terrain.batch.importTitle')}
        open={batchImportVisible}
        onCancel={() => {
          try {
            setBatchImportVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setBatchImportVisible(false)}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="import"
            type="primary"
            onClick={() => handleBatchImport(activeTab as any)}
            disabled={batchFiles.length === 0}
          >
            {t('terrain.batch.import')}
          </Button>
        ]}
      >
        <Upload.Dragger
          multiple
          showUploadList={{ showRemoveIcon: true }}
          fileList={batchFiles.map((file, index) => ({
            uid: `-${index}`,
            name: file.name,
            status: 'done',
            size: file.size,
            type: file.type
          }))}
          beforeUpload={(file) => {
            setBatchFiles([...batchFiles, file]);
            return false;
          }}
          onRemove={(file) => {
            const index = batchFiles.findIndex(f => f.name === file.name);
            if (index !== -1) {
              const newFiles = [...batchFiles];
              newFiles.splice(index, 1);
              setBatchFiles(newFiles);
            }
          }}
        >
          <p className="ant-upload-drag-icon">
            <FileZipOutlined />
          </p>
          <p className="ant-upload-text">{t('terrain.batch.dragFiles')}</p>
          <p className="ant-upload-hint">{t('terrain.batch.dragFilesHint')}</p>
        </Upload.Dragger>

        {batchResults.length > 0 && (
          <>
            <Divider />

            <Table
              dataSource={batchResults.map((result, index) => ({
                key: index,
                ...result
              }))}
              columns={[
                {
                  title: t('terrain.batch.fileName'),
                  dataIndex: 'fileName',
                  key: 'fileName'
                },
                {
                  title: t('terrain.batch.status'),
                  dataIndex: 'success',
                  key: 'success',
                  render: (success) => (
                    <span style={{ color: success ? '#52c41a' : '#f5222d' }}>
                      {success ? t('terrain.batch.success') : t('terrain.batch.failed')}
                    </span>
                  )
                },
                {
                  title: t('terrain.batch.error'),
                  dataIndex: 'error',
                  key: 'error'
                }
              ]}
              pagination={false}
              size="small"
            />
          </>
        )}
      </Modal>

      {/* 批量导出模态框 */}
      <Modal
        title={t('terrain.batch.exportTitle')}
        open={batchExportVisible}
        onCancel={() => {
          try {
            setBatchExportVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setBatchExportVisible(false)}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="export"
            type="primary"
            onClick={() => handleBatchExport(activeTab as any)}
            disabled={batchExportFormats.length === 0}
          >
            {t('terrain.batch.export')}
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item label={t('terrain.batch.selectFormats')}>
            <Checkbox.Group
              options={getFormatOptions()}
              value={batchExportFormats}
              onChange={(values) => setBatchExportFormats(values as string[])}
            />
          </Form.Item>

          <Form.Item label={t('terrain.batch.exportOptions')}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {getExportOptions().map((option, index) => (
                <div key={index}>
                  <Checkbox
                    checked={exportOptions[option.key]}
                    onChange={(e) => handleExportOptionChange(option.key, e.target.checked)}
                  >
                    {option.label}
                  </Checkbox>
                </div>
              ))}
            </Space>
          </Form.Item>
        </Form>

        {batchExportResults.length > 0 && (
          <>
            <Divider />

            <Table
              dataSource={batchExportResults.map((result, index) => ({
                key: index,
                ...result
              }))}
              columns={[
                {
                  title: t('terrain.batch.fileName'),
                  dataIndex: 'fileName',
                  key: 'fileName'
                },
                {
                  title: t('terrain.batch.status'),
                  dataIndex: 'success',
                  key: 'success',
                  render: (success) => (
                    <span style={{ color: success ? '#52c41a' : '#f5222d' }}>
                      {success ? t('terrain.batch.success') : t('terrain.batch.failed')}
                    </span>
                  )
                },
                {
                  title: t('terrain.batch.error'),
                  dataIndex: 'error',
                  key: 'error'
                }
              ]}
              pagination={false}
              size="small"
            />
          </>
        )}
      </Modal>
    </div>
  );
};

export default TerrainImportExportPanel;
