/**
 * 控制器预设选择器组件
 * 用于选择和应用角色控制器预设
 */
import React, { useState, useEffect } from 'react';
import { Modal, List, Card, Button, Select, Tag, Tabs, Empty, Spin, message, Input } from 'antd';
import { StarOutlined, StarFilled, InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { Search } = Input;

// 预设类型枚举
enum ControllerPresetType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  SPECIAL = 'special',
  CUSTOM = 'custom',
  FIRST_PERSON = 'first_person',
  THIRD_PERSON = 'third_person',
  FLYING = 'flying',
  PHYSICS_INTERACTION = 'physics_interaction',
  ENVIRONMENT_AWARE = 'environment_aware'
}

// 预设数据接口
interface ControllerPresetData {
  id: string;
  name: string;
  description: string;
  type: ControllerPresetType;
  tags: string[];
  config: Record<string, any>;
  thumbnail?: string;
  author: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ControllerPresetSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (presetId: string) => void;
}

/**
 * 控制器预设选择器组件
 */
const ControllerPresetSelector: React.FC<ControllerPresetSelectorProps> = ({ visible, onClose, onSelect }) => {
  const { t } = useTranslation();

  // 预设列表
  const [presets, setPresets] = useState<ControllerPresetData[]>([]);

  // 加载状态
  const [loading, setLoading] = useState(true);

  // 搜索关键字
  const [searchKeyword, setSearchKeyword] = useState('');

  // 选中的预设类型
  const [selectedType, setSelectedType] = useState<ControllerPresetType | 'all'>('all');

  // 选中的标签
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // 收藏的预设
  const [favoritePresets, setFavoritePresets] = useState<string[]>([]);

  // 所有标签
  const [allTags, setAllTags] = useState<string[]>([]);

  // 当前选中的预设
  const [selectedPreset, setSelectedPreset] = useState<ControllerPresetData | null>(null);

  // 加载预设
  useEffect(() => {
    // 模拟从预设管理器加载预设
    // 实际实现应该调用预设管理器的API
    const loadPresets = async () => {
      try {
        setLoading(true);

        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 模拟预设数据
        const mockPresets: ControllerPresetData[] = [
          {
            id: 'basic_character',
            name: '基础角色控制器',
            description: '标准的角色控制器预设，适用于大多数角色',
            type: ControllerPresetType.BASIC,
            tags: ['基础', '标准', '通用'],
            config: {
              walkSpeed: 2.0,
              runSpeed: 5.0,
              jumpForce: 5.0,
              gravity: 9.8,
              useStateMachine: true,
              useBlendSpace: true
            },
            thumbnail: 'assets/thumbnails/basic_character.jpg',
            author: '系统',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'advanced_character',
            name: '高级角色控制器',
            description: '高级角色控制器预设，包含更多功能和更好的物理交互',
            type: ControllerPresetType.ADVANCED,
            tags: ['高级', '物理', '交互'],
            config: {
              walkSpeed: 2.0,
              runSpeed: 5.0,
              crouchSpeed: 1.0,
              jumpForce: 5.0,
              gravity: 9.8,
              turnSpeed: 2.0,
              airControl: 0.5,
              usePhysics: true,
              useStateMachine: true,
              useBlendSpace: true,
              useIK: true,
              useEnvironmentAwareness: true
            },
            thumbnail: 'assets/thumbnails/advanced_character.jpg',
            author: '系统',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'first_person_character',
            name: '第一人称角色控制器',
            description: '第一人称角色控制器预设，适用于FPS游戏',
            type: ControllerPresetType.FIRST_PERSON,
            tags: ['第一人称', 'FPS', '射击'],
            config: {
              walkSpeed: 2.0,
              runSpeed: 5.0,
              jumpForce: 5.0,
              gravity: 9.8,
              usePhysics: true,
              useStateMachine: true,
              useBlendSpace: false,
              useIK: false,
              useEnvironmentAwareness: false,
              cameraMode: 'first_person',
              headBobbing: true,
              headBobbingAmount: 0.1,
              headBobbingSpeed: 10.0,
              mouseSensitivity: 0.1
            },
            thumbnail: 'assets/thumbnails/first_person_character.jpg',
            author: '系统',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'third_person_character',
            name: '第三人称角色控制器',
            description: '第三人称角色控制器预设，适用于动作冒险游戏',
            type: ControllerPresetType.THIRD_PERSON,
            tags: ['第三人称', '动作', '冒险'],
            config: {
              walkSpeed: 2.0,
              runSpeed: 5.0,
              jumpForce: 5.0,
              gravity: 9.8,
              usePhysics: true,
              useStateMachine: true,
              useBlendSpace: true,
              useIK: true,
              useEnvironmentAwareness: false,
              cameraMode: 'third_person',
              cameraDistance: 5.0,
              cameraHeight: 2.0,
              cameraSmoothing: 0.2,
              cameraPitchLimit: { min: -30, max: 60 }
            },
            thumbnail: 'assets/thumbnails/third_person_character.jpg',
            author: '系统',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'physics_interaction_character',
            name: '物理交互角色控制器',
            description: '物理交互角色控制器预设，适用于需要与物理对象交互的场景',
            type: ControllerPresetType.PHYSICS_INTERACTION,
            tags: ['物理', '交互', '抓取'],
            config: {
              walkSpeed: 2.0,
              runSpeed: 5.0,
              jumpForce: 5.0,
              gravity: 9.8,
              usePhysics: true,
              useStateMachine: true,
              useBlendSpace: true,
              useIK: true,
              useEnvironmentAwareness: true,
              interactionRange: 2.0,
              maxForce: 1000,
              maxTorque: 500,
              useRagdoll: true,
              ragdollTransitionTime: 0.5
            },
            thumbnail: 'assets/thumbnails/physics_interaction_character.jpg',
            author: '系统',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'environment_aware_character',
            name: '环境感知角色控制器',
            description: '环境感知角色控制器预设，适用于需要对环境做出响应的场景',
            type: ControllerPresetType.ENVIRONMENT_AWARE,
            tags: ['环境', '感知', '响应'],
            config: {
              walkSpeed: 2.0,
              runSpeed: 5.0,
              jumpForce: 5.0,
              gravity: 9.8,
              usePhysics: true,
              useStateMachine: true,
              useBlendSpace: true,
              useIK: true,
              useEnvironmentAwareness: true,
              awarenessRange: 50,
              updateFrequency: 1000,
              autoDetect: true,
              autoRespond: true
            },
            thumbnail: 'assets/thumbnails/environment_aware_character.jpg',
            author: '系统',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        setPresets(mockPresets);

        // 提取所有标签
        const tags = new Set<string>();
        mockPresets.forEach(preset => {
          preset.tags.forEach(tag => tags.add(tag));
        });
        setAllTags(Array.from(tags));

        // 加载收藏的预设
        const storedFavorites = localStorage.getItem('favoriteControllerPresets');
        if (storedFavorites) {
          setFavoritePresets(JSON.parse(storedFavorites));
        }

        setLoading(false);
      } catch (error) {
        console.error('加载预设失败:', error);
        message.error(t('editor.avatar.loadPresetsFailed'));
        setLoading(false);
      }
    };

    if (visible) {
      loadPresets();
    }
  }, [visible, t]);

  // 过滤预设
  const filteredPresets = presets.filter(preset => {
    // 按关键字过滤
    const matchesKeyword = searchKeyword === '' ||
      preset.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      preset.description.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      preset.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()));

    // 按类型过滤
    const matchesType = selectedType === 'all' || preset.type === selectedType;

    // 按标签过滤
    const matchesTags = selectedTags.length === 0 ||
      selectedTags.every(tag => preset.tags.includes(tag));

    return matchesKeyword && matchesType && matchesTags;
  });

  // 收藏的预设
  const favoritePresetsList = presets.filter(preset => favoritePresets.includes(preset.id));

  // 切换收藏状态
  const toggleFavorite = (presetId: string) => {
    let newFavorites: string[];

    if (favoritePresets.includes(presetId)) {
      newFavorites = favoritePresets.filter(id => id !== presetId);
    } else {
      newFavorites = [...favoritePresets, presetId];
    }

    setFavoritePresets(newFavorites);
    localStorage.setItem('favoriteControllerPresets', JSON.stringify(newFavorites));
  };

  // 渲染预设卡片
  const renderPresetCard = (preset: ControllerPresetData) => {
    const isFavorite = favoritePresets.includes(preset.id);

    return (
      <Card
        hoverable
        style={{ width: 240, marginBottom: 16 }}
        cover={preset.thumbnail ? <img alt={preset.name} src={preset.thumbnail} /> : null}
        actions={[
          <Button
            key="select"
            type="primary"
            size="small"
            onClick={() => onSelect(preset.id)}
          >
            {t('editor.common.apply')}
          </Button>,
          <Button
            key="info"
            type="text"
            size="small"
            icon={<InfoCircleOutlined />}
            onClick={() => setSelectedPreset(preset)}
          />,
          <Button
            key="favorite"
            type="text"
            size="small"
            icon={isFavorite ? <StarFilled /> : <StarOutlined />}
            onClick={() => toggleFavorite(preset.id)}
          />
        ]}
      >
        <Card.Meta
          title={preset.name}
          description={
            <>
              <div className="preset-description">{preset.description}</div>
              <div className="preset-tags">
                {preset.tags.map(tag => (
                  <Tag key={tag} color="blue">{tag}</Tag>
                ))}
              </div>
              <div className="preset-author">{t('editor.common.author')}: {preset.author}</div>
            </>
          }
        />
      </Card>
    );
  };

  // 标签页配置
  const tabItems = [
    {
      key: 'all',
      label: t('editor.common.all'),
      children: loading ? (
        <div className="preset-loading">
          <Spin />
          <div>{t('editor.common.loading')}</div>
        </div>
      ) : filteredPresets.length > 0 ? (
        <List
          grid={{ gutter: 16, column: 3 }}
          dataSource={filteredPresets}
          renderItem={renderPresetCard}
        />
      ) : (
        <Empty description={t('editor.avatar.noPresetsFound') as string} />
      )
    },
    {
      key: 'favorites',
      label: t('editor.avatar.favorites'),
      children: loading ? (
        <div className="preset-loading">
          <Spin />
          <div>{t('editor.common.loading')}</div>
        </div>
      ) : favoritePresetsList.length > 0 ? (
        <List
          grid={{ gutter: 16, column: 3 }}
          dataSource={favoritePresetsList}
          renderItem={renderPresetCard}
        />
      ) : (
        <Empty description={t('editor.avatar.noFavoritePresets') as string} />
      )
    }
  ];

  return (
    <Modal
      title={t('editor.avatar.selectControllerPreset')}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('editor.common.close')}
        </Button>
      ]}
    >
      <div className="preset-selector-container">
        <div className="preset-selector-filters">
          <Search
            placeholder={t('editor.common.search') as string}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ marginBottom: 16 }}
          />

          <Select
            placeholder={t('editor.avatar.selectPresetType') as string}
            value={selectedType}
            onChange={setSelectedType}
            style={{ width: '100%', marginBottom: 16 }}
          >
            <Option value="all">{t('editor.common.all')}</Option>
            <Option value={ControllerPresetType.BASIC}>{t('editor.avatar.basicPreset')}</Option>
            <Option value={ControllerPresetType.ADVANCED}>{t('editor.avatar.advancedPreset')}</Option>
            <Option value={ControllerPresetType.SPECIAL}>{t('editor.avatar.specialPreset')}</Option>
            <Option value={ControllerPresetType.CUSTOM}>{t('editor.avatar.customPreset')}</Option>
            <Option value={ControllerPresetType.FIRST_PERSON}>{t('editor.avatar.firstPersonPreset')}</Option>
            <Option value={ControllerPresetType.THIRD_PERSON}>{t('editor.avatar.thirdPersonPreset')}</Option>
            <Option value={ControllerPresetType.FLYING}>{t('editor.avatar.flyingPreset')}</Option>
            <Option value={ControllerPresetType.PHYSICS_INTERACTION}>{t('editor.avatar.physicsInteractionPreset')}</Option>
            <Option value={ControllerPresetType.ENVIRONMENT_AWARE}>{t('editor.avatar.environmentAwarePreset')}</Option>
          </Select>

          <Select
            mode="multiple"
            placeholder={t('editor.avatar.selectTags') as string}
            value={selectedTags}
            onChange={setSelectedTags}
            style={{ width: '100%', marginBottom: 16 }}
          >
            {allTags.map(tag => (
              <Option key={tag} value={tag}>{tag}</Option>
            ))}
          </Select>
        </div>

        <div className="preset-selector-content">
          <Tabs
            defaultActiveKey="all"
            items={tabItems}
          />
        </div>
      </div>

      {/* 预设详情对话框 */}
      {selectedPreset && (
        <Modal
          title={selectedPreset.name}
          open={!!selectedPreset}
          onCancel={() => {
          try {
            setSelectedPreset(null)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
          footer={[
            <Button key="close" onClick={() => setSelectedPreset(null)}>
              {t('editor.common.close')}
            </Button>,
            <Button key="apply" type="primary" onClick={() => {
              onSelect(selectedPreset.id);
              setSelectedPreset(null);
            }}>
              {t('editor.common.apply')}
            </Button>
          ]}
        >
          <div className="preset-detail">
            {selectedPreset.thumbnail && (
              <img
                src={selectedPreset.thumbnail}
                alt={selectedPreset.name}
                style={{ maxWidth: '100%', marginBottom: 16 }}
              />
            )}

            <h3>{t('editor.common.description')}</h3>
            <p>{selectedPreset.description}</p>

            <h3>{t('editor.common.type')}</h3>
            <p>{t(`editor.avatar.${selectedPreset.type}Preset`)}</p>

            <h3>{t('editor.common.tags')}</h3>
            <div>
              {selectedPreset.tags.map(tag => (
                <Tag key={tag} color="blue">{tag}</Tag>
              ))}
            </div>

            <h3>{t('editor.common.author')}</h3>
            <p>{selectedPreset.author}</p>

            <h3>{t('editor.common.createdAt')}</h3>
            <p>{selectedPreset.createdAt.toLocaleDateString()}</p>

            <h3>{t('editor.common.updatedAt')}</h3>
            <p>{selectedPreset.updatedAt.toLocaleDateString()}</p>
          </div>
        </Modal>
      )}
    </Modal>
  );
};

export default ControllerPresetSelector;
