/**
 * 面部动画预设面板
 * 用于管理面部动画预设和模板
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Tabs,
  Button,
  Space,
  Input,
  Select,
  Tooltip,
  Badge,
  Dropdown,

  message,
  Modal,
  Card,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  StarOutlined,
  StarFilled,
  HistoryOutlined,
  DownloadOutlined,
  SettingOutlined,
  SearchOutlined,
  AppstoreOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  DeleteOutlined,
  DownOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FacialAnimationPresetManager, FacialAnimationPresetType } from '../components/FacialAnimationEditor/FacialAnimationPresetManager';
import { FacialAnimationTemplateManager } from '../components/FacialAnimationEditor/FacialAnimationTemplateManager';
import { facialAnimationPresetService } from '../services/FacialAnimationPresetService';
import './FacialAnimationPresetPanel.less';

const { Option } = Select;

/**
 * 预设历史记录项
 */
interface PresetHistoryItem {
  id: string;
  presetId: string;
  presetName: string;
  appliedAt: Date;
  entityId: string;
}

/**
 * 面部动画预设面板属性
 */
interface FacialAnimationPresetPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 默认活动标签 */
  defaultActiveTab?: string;
  /** 默认预设类型 */
  defaultPresetType?: FacialAnimationPresetType;
  /** 默认文化 */
  defaultCulture?: string;
  /** 是否显示统计信息 */
  showStatistics?: boolean;
  /** 是否显示快速操作栏 */
  showQuickActions?: boolean;
  /** 是否显示收藏功能 */
  showFavorites?: boolean;
  /** 是否显示历史记录 */
  showHistory?: boolean;
  /** 是否启用批量操作 */
  enableBatchOperations?: boolean;
  /** 应用预设回调 */
  onPresetApply?: (preset: any) => void;
  /** 应用模板回调 */
  onTemplateApply?: (template: any, parameters: any) => void;
  /** 预设收藏状态变更回调 */
  onPresetFavoriteChange?: (presetId: string, isFavorite: boolean) => void;
  /** 批量操作回调 */
  onBatchOperation?: (operation: string, presetIds: string[]) => void;
}

/**
 * 面部动画预设面板
 */
export const FacialAnimationPresetPanel: React.FC<FacialAnimationPresetPanelProps> = ({
  entityId,
  editable = true,
  defaultActiveTab = 'presets',
  defaultPresetType = FacialAnimationPresetType.STANDARD,
  defaultCulture = 'global',
  showStatistics = true,
  showQuickActions = true,
  showFavorites = true,
  showHistory = true,
  enableBatchOperations = true,
  onPresetApply,
  onTemplateApply,
  onPresetFavoriteChange,
  onBatchOperation
}) => {
  const { t } = useTranslation();

  // 基础状态
  const [activeTab, setActiveTab] = useState<string>(defaultActiveTab);
  const [loading, setLoading] = useState<boolean>(false);

  // 搜索和筛选状态
  const [searchText, setSearchText] = useState<string>('');
  const [filterType, setFilterType] = useState<FacialAnimationPresetType | 'all'>('all');
  const [filterCulture, setFilterCulture] = useState<string>('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState<boolean>(false);

  // 批量操作状态
  const [selectedPresets, setSelectedPresets] = useState<string[]>([]);
  const [batchMode, setBatchMode] = useState<boolean>(false);

  // 统计信息状态
  const [statistics, setStatistics] = useState({
    totalPresets: 0,
    totalTemplates: 0,
    favoritePresets: 0,
    recentlyUsed: 0
  });

  // 历史记录状态
  const [history, setHistory] = useState<PresetHistoryItem[]>([]);
  const [showHistoryModal, setShowHistoryModal] = useState<boolean>(false);

  // 快速预览状态
  const [quickPreviewVisible, setQuickPreviewVisible] = useState<boolean>(false);
  const [previewPreset, setPreviewPreset] = useState<any>(null);

  // 初始化和数据加载
  useEffect(() => {
    loadStatistics();
    loadHistory();
  }, []);

  // 加载统计信息
  const loadStatistics = useCallback(async () => {
    try {
      setLoading(true);
      const presets = await facialAnimationPresetService.getAllPresets();
      // 这里应该从模板服务获取模板数量
      const templateCount = 10; // 临时数据

      setStatistics({
        totalPresets: presets.length,
        totalTemplates: templateCount,
        favoritePresets: presets.filter(p => p.tags?.includes('favorite')).length,
        recentlyUsed: history.length
      });
    } catch (error) {
      console.error('加载统计信息失败:', error);
    } finally {
      setLoading(false);
    }
  }, [history.length]);

  // 加载历史记录
  const loadHistory = useCallback(async () => {
    try {
      // 从本地存储或服务加载历史记录
      const savedHistory = localStorage.getItem('facial-animation-preset-history');
      if (savedHistory) {
        setHistory(JSON.parse(savedHistory));
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
    }
  }, []);

  // 添加到历史记录
  const addToHistory = useCallback((presetId: string, presetName: string) => {
    const newItem: PresetHistoryItem = {
      id: `${Date.now()}-${presetId}`,
      presetId,
      presetName,
      appliedAt: new Date(),
      entityId: entityId || 'unknown'
    };

    const updatedHistory = [newItem, ...history.slice(0, 19)]; // 保留最近20条
    setHistory(updatedHistory);
    localStorage.setItem('facial-animation-preset-history', JSON.stringify(updatedHistory));
  }, [history, entityId]);

  // 处理预设收藏状态变更
  const handlePresetFavoriteChange = useCallback((presetId: string, isFavorite: boolean) => {
    if (onPresetFavoriteChange) {
      onPresetFavoriteChange(presetId, isFavorite);
    }
  }, [onPresetFavoriteChange]);

  // 处理预设应用
  const handlePresetApply = useCallback((preset: any) => {
    if (onPresetApply) {
      onPresetApply(preset);
    }
    addToHistory(preset.id, preset.name);
    message.success(t('editor.animation.presetApplied', { name: preset.name }));
  }, [onPresetApply, addToHistory, t]);

  // 处理模板应用
  const handleTemplateApply = useCallback((template: any, parameters: any) => {
    if (onTemplateApply) {
      onTemplateApply(template, parameters);
    }
    addToHistory(template.id, template.name);
    message.success(t('editor.animation.templateApplied', { name: template.name }));
  }, [onTemplateApply, addToHistory, t]);



  // 处理批量操作
  const handleBatchOperation = useCallback((operation: string) => {
    if (selectedPresets.length === 0) {
      message.warning(t('editor.animation.noPresetsSelected'));
      return;
    }

    if (onBatchOperation) {
      onBatchOperation(operation, selectedPresets);
    }

    switch (operation) {
      case 'export':
        message.success(t('editor.animation.batchExportSuccess', { count: selectedPresets.length }));
        break;
      case 'delete':
        message.success(t('editor.animation.batchDeleteSuccess', { count: selectedPresets.length }));
        break;
      case 'favorite':
        message.success(t('editor.animation.batchFavoriteSuccess', { count: selectedPresets.length }));
        break;
    }

    setSelectedPresets([]);
    setBatchMode(false);
    loadStatistics();
  }, [selectedPresets, onBatchOperation, t, loadStatistics]);

  // 快速预览
  const handleQuickPreview = useCallback((preset: any) => {
    setPreviewPreset(preset);
    setQuickPreviewVisible(true);
  }, []);

  // 清空历史记录
  const clearHistory = useCallback(() => {
    setHistory([]);
    localStorage.removeItem('facial-animation-preset-history');
    message.success(t('editor.animation.historyCleared'));
  }, [t]);

  // 渲染统计信息卡片
  const renderStatisticsCard = () => {
    if (!showStatistics) return null;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title={t('editor.animation.totalPresets')}
              value={statistics.totalPresets}
              prefix={<StarOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('editor.animation.totalTemplates')}
              value={statistics.totalTemplates}
              prefix={<AppstoreOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('editor.animation.favoritePresets')}
              value={statistics.favoritePresets}
              prefix={<StarFilled />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('editor.animation.recentlyUsed')}
              value={statistics.recentlyUsed}
              prefix={<HistoryOutlined />}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染快速操作栏
  const renderQuickActions = () => {
    if (!showQuickActions) return null;

    const batchMenuItems = [
      {
        key: 'export',
        label: t('editor.animation.batchExport'),
        icon: <DownloadOutlined />,
        onClick: () => handleBatchOperation('export')
      },
      {
        key: 'favorite',
        label: t('editor.animation.batchFavorite'),
        icon: <StarOutlined />,
        onClick: () => handleBatchOperation('favorite')
      },
      {
        key: 'delete',
        label: t('editor.animation.batchDelete'),
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleBatchOperation('delete')
      }
    ];

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input.Search
            placeholder={t('editor.animation.searchPresets') || '搜索预设'}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 200 }}
            prefix={<SearchOutlined />}
          />

          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: 150 }}
            placeholder={t('editor.animation.filterByType')}
          >
            <Option value="all">{t('editor.animation.allTypes')}</Option>
            <Option value={FacialAnimationPresetType.STANDARD}>{t('editor.animation.standard')}</Option>
            <Option value={FacialAnimationPresetType.CULTURAL}>{t('editor.animation.cultural')}</Option>
            <Option value={FacialAnimationPresetType.EMOTION_COMBO}>{t('editor.animation.emotionCombo')}</Option>
            <Option value={FacialAnimationPresetType.ANIMATION_SEQUENCE}>{t('editor.animation.animationSequence')}</Option>
            <Option value={FacialAnimationPresetType.CUSTOM}>{t('editor.animation.custom')}</Option>
          </Select>

          <Select
            value={filterCulture}
            onChange={setFilterCulture}
            style={{ width: 120 }}
            placeholder={t('editor.animation.filterByCulture')}
          >
            <Option value="all">{t('editor.animation.allCultures')}</Option>
            <Option value="global">{t('editor.animation.global')}</Option>
            <Option value="chinese">{t('editor.animation.chinese')}</Option>
            <Option value="japanese">{t('editor.animation.japanese')}</Option>
            <Option value="american">{t('editor.animation.american')}</Option>
          </Select>

          {showFavorites && (
            <Button
              type={showFavoritesOnly ? 'primary' : 'default'}
              icon={showFavoritesOnly ? <StarFilled /> : <StarOutlined />}
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            >
              {t('editor.animation.favorites')}
            </Button>
          )}

          {showHistory && (
            <Button
              icon={<HistoryOutlined />}
              onClick={() => setShowHistoryModal(true)}
            >
              {t('editor.animation.history')}
              {history.length > 0 && <Badge count={history.length} style={{ marginLeft: 8 }} />}
            </Button>
          )}

          {enableBatchOperations && (
            <>
              <Button
                type={batchMode ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setBatchMode(!batchMode)}
              >
                {t('editor.animation.batchMode')}
              </Button>

              {batchMode && selectedPresets.length > 0 && (
                <Dropdown menu={{ items: batchMenuItems }} trigger={['click']}>
                  <Button>
                    {t('editor.animation.batchActions')} ({selectedPresets.length})
                    <DownOutlined />
                  </Button>
                </Dropdown>
              )}
            </>
          )}

          <Button
            icon={<ReloadOutlined />}
            onClick={loadStatistics}
            loading={loading}
          >
            {t('editor.animation.refresh')}
          </Button>
        </Space>
      </Card>
    );
  };

  // 渲染历史记录模态框
  const renderHistoryModal = () => {
    return (
      <Modal
        title={t('editor.animation.presetHistory')}
        open={showHistoryModal}
        onCancel={() => {
          try {
            setShowHistoryModal(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={[
          <Button key="clear" onClick={clearHistory} danger>
            {t('editor.animation.clearHistory')}
          </Button>,
          <Button key="close" onClick={() => setShowHistoryModal(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={600}
      >
        <div style={{ maxHeight: 400, overflowY: 'auto' }}>
          {history.length === 0 ? (
            <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
              {t('editor.animation.noHistory')}
            </div>
          ) : (
            history.map((item) => (
              <Card
                key={item.id}
                size="small"
                style={{ marginBottom: 8 }}
                actions={[
                  <Button
                    type="link"
                    size="small"
                    onClick={() => {
                      // 重新应用历史预设
                      if (onPresetApply) {
                        onPresetApply({ id: item.presetId, name: item.presetName });
                      }
                      setShowHistoryModal(false);
                    }}
                  >
                    {t('editor.animation.reapply')}
                  </Button>
                ]}
              >
                <Card.Meta
                  title={item.presetName}
                  description={
                    <div>
                      <div>{t('editor.animation.appliedAt')}: {item.appliedAt.toLocaleString()}</div>
                      <div>{t('editor.animation.entity')}: {item.entityId}</div>
                    </div>
                  }
                />
              </Card>
            ))
          )}
        </div>
      </Modal>
    );
  };

  // 渲染快速预览模态框
  const renderQuickPreviewModal = () => {
    return (
      <Modal
        title={t('editor.animation.quickPreview')}
        open={quickPreviewVisible}
        onCancel={() => {
          try {
            setQuickPreviewVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={[
          <Button
            key="apply"
            type="primary"
            onClick={() => {
              if (previewPreset && onPresetApply) {
                onPresetApply(previewPreset);
                addToHistory(previewPreset.id, previewPreset.name);
              }
              setQuickPreviewVisible(false);
            }}
          >
            {t('editor.animation.applyPreset')}
          </Button>,
          <Button key="close" onClick={() => setQuickPreviewVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={800}
      >
        {previewPreset && (
          <div>
            <h4>{previewPreset.name}</h4>
            <p>{previewPreset.description}</p>
            {/* 这里可以添加3D预览组件 */}
            <div style={{ height: 300, background: '#f5f5f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <div style={{ textAlign: 'center' }}>
                <PlayCircleOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                <div style={{ marginTop: 16 }}>{t('editor.animation.previewPlaceholder')}</div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    );
  };
  
  return (
    <div className="facial-animation-preset-panel">
      {/* 统计信息卡片 */}
      {renderStatisticsCard()}

      {/* 快速操作栏 */}
      {renderQuickActions()}

      {/* 主要内容标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
        tabBarExtraContent={
          <Space>
            {batchMode && selectedPresets.length > 0 && (
              <Badge count={selectedPresets.length}>
                <Button size="small" type="primary">
                  {t('editor.animation.selected')}
                </Button>
              </Badge>
            )}
            <Tooltip title={t('editor.animation.settings')}>
              <Button
                size="small"
                icon={<SettingOutlined />}
                onClick={() => {
                  // 打开设置面板
                  message.info(t('editor.animation.settingsComingSoon'));
                }}
              />
            </Tooltip>
          </Space>
        }
        items={[
          {
            key: 'presets',
            label: (
              <span>
                <StarOutlined />
                {t('editor.animation.presets')}
                {statistics.totalPresets > 0 && (
                  <Badge count={statistics.totalPresets} style={{ marginLeft: 8 }} />
                )}
              </span>
            ),
            children: (
              <FacialAnimationPresetManager
                entityId={entityId}
                editable={editable}
                defaultPresetType={filterType === 'all' ? defaultPresetType : filterType}
                defaultCulture={filterCulture === 'all' ? defaultCulture : filterCulture}
                onPresetApply={handlePresetApply}
                onPresetImport={(presets) => {
                  loadStatistics();
                  message.success(t('editor.animation.presetsImported', { count: presets.length }));
                }}
                onPresetExport={(presets) => {
                  message.success(t('editor.animation.presetsExported', { count: presets.length }));
                }}
                onPresetFavoriteChange={handlePresetFavoriteChange}
              />
            )
          },
          {
            key: 'templates',
            label: (
              <span>
                <AppstoreOutlined />
                {t('editor.animation.templates')}
                {statistics.totalTemplates > 0 && (
                  <Badge count={statistics.totalTemplates} style={{ marginLeft: 8 }} />
                )}
              </span>
            ),
            children: (
              <FacialAnimationTemplateManager
                entityId={entityId}
                editable={editable}
                onTemplateApply={handleTemplateApply}
                onTemplateImport={(templates) => {
                  loadStatistics();
                  message.success(t('editor.animation.templatesImported', { count: templates.length }));
                }}
                onTemplateExport={(templates) => {
                  message.success(t('editor.animation.templatesExported', { count: templates.length }));
                }}
              />
            )
          },
          ...(showHistory && history.length > 0 ? [{
            key: 'recent',
            label: (
              <span>
                <HistoryOutlined />
                {t('editor.animation.recentlyUsed')}
                <Badge count={Math.min(history.length, 99)} style={{ marginLeft: 8 }} />
              </span>
            ),
            children: (
              <div style={{ padding: 16 }}>
                <Row gutter={[16, 16]}>
                  {history.slice(0, 12).map((item) => (
                    <Col key={item.id} xs={24} sm={12} md={8} lg={6}>
                      <Card
                        size="small"
                        hoverable
                        actions={[
                          <Button
                            type="link"
                            size="small"
                            onClick={() => handleQuickPreview({ id: item.presetId, name: item.presetName })}
                          >
                            {t('editor.animation.preview')}
                          </Button>,
                          <Button
                            type="link"
                            size="small"
                            onClick={() => {
                              if (onPresetApply) {
                                onPresetApply({ id: item.presetId, name: item.presetName });
                              }
                              addToHistory(item.presetId, item.presetName);
                            }}
                          >
                            {t('editor.animation.apply')}
                          </Button>
                        ]}
                      >
                        <Card.Meta
                          title={item.presetName}
                          description={
                            <div>
                              <div style={{ fontSize: 12, color: '#999' }}>
                                {item.appliedAt.toLocaleDateString()}
                              </div>
                              <div style={{ fontSize: 12, color: '#666' }}>
                                {item.entityId}
                              </div>
                            </div>
                          }
                        />
                      </Card>
                    </Col>
                  ))}
                </Row>

                {history.length > 12 && (
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button onClick={() => setShowHistoryModal(true)}>
                      {t('editor.animation.viewAllHistory')} ({history.length})
                    </Button>
                  </div>
                )}
              </div>
            )
          }] : [])
        ]}
      />

      {/* 模态框 */}
      {renderHistoryModal()}
      {renderQuickPreviewModal()}
    </div>
  );
};
