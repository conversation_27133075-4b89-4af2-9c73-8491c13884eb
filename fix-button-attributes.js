const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'editor/src/components/avatar/ControllerPresetSelector.tsx',
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  'editor/src/components/ExampleBrowser/ImportDialog.tsx',
  'editor/src/components/feedback/AnimationFeedbackForm.tsx',
  'editor/src/components/feedback/ContextAwareFeedbackForm.tsx',
  'editor/src/components/feedback/FeedbackForm.tsx',
  'editor/src/components/git/GitHistoryPanel.tsx',
  'editor/src/components/layout/MobileAdaptiveLayout.tsx',
  'editor/src/components/network/NetworkSimulatorPanel.tsx',
  'editor/src/components/rendering/water/WaterMaterialPresetSelector.tsx',
  'editor/src/components/testing/FeedbackForm.tsx',
  'editor/src/components/tutorials/TutorialPanel.tsx',
  'editor/src/pages/AnimationLibraryPage.tsx',
  'editor/src/pages/ParticleLibraryPage.tsx'
];

function fixButtonAttributes(content) {
  // 移除Button组件上错误的Modal属性
  content = content.replace(
    /(<Button[^>]*?) destroyOnClose keyboard=\{true\} maskClosable=\{true\}([^>]*>)/g,
    '$1$2'
  );

  // 移除Space组件上错误的Modal属性
  content = content.replace(
    /(<Space[^>]*?) destroyOnClose keyboard=\{true\} maskClosable=\{true\}([^>]*>)/g,
    '$1$2'
  );

  // 移除div组件上错误的Modal属性
  content = content.replace(
    /(<div[^>]*?) destroyOnClose keyboard=\{true\} maskClosable=\{true\}([^>]*>)/g,
    '$1$2'
  );

  return content;
}

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`文件不存在: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixButtonAttributes(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ 修复完成: ${filePath}`);
      return true;
    } else {
      console.log(`⚪ 无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复失败: ${filePath}`, error.message);
    return false;
  }
}

function main() {
  console.log('开始批量修复 Button 属性错误...\n');
  
  let fixedCount = 0;
  let totalCount = 0;

  for (const file of filesToFix) {
    totalCount++;
    if (fixFile(file)) {
      fixedCount++;
    }
  }

  console.log(`\n修复完成！`);
  console.log(`总文件数: ${totalCount}`);
  console.log(`修复文件数: ${fixedCount}`);
  console.log(`跳过文件数: ${totalCount - fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { fixButtonAttributes, fixFile };
