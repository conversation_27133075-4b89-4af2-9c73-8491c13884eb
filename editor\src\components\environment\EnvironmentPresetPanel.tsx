/**
 * 环境预设面板
 * 
 * 该面板提供预定义的环境预设，用户可以快速应用这些预设到角色上。
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Divider, 
  Input, 
  Modal, 
  Form,
  Select,
  message,
  Collapse,
  Badge
} from 'antd';
import {
  AppstoreOutlined,
  PlusOutlined,
  SearchOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

// 导入类型定义
import {
  ResponseType,
  ResponsePriority,
  type EnvironmentResponseRule
} from './types';

const { Search } = Input;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * 环境预设面板属性接口
 */
interface EnvironmentPresetPanelProps {
  entityId?: string;
  onApplyPreset?: (preset: EnvironmentResponseRule) => void;
  onSaveCustomPreset?: (preset: EnvironmentResponseRule) => void;
  onDeleteCustomPreset?: (presetId: string) => void;
  customPresets?: EnvironmentResponseRule[];
}

/**
 * 环境预设面板组件
 */
const EnvironmentPresetPanel: React.FC<EnvironmentPresetPanelProps> = ({
  onApplyPreset,
  onSaveCustomPreset,
  onDeleteCustomPreset,
  customPresets = []
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 翻译辅助函数
  const translate = (key: string, defaultValue: string = ''): string => {
    const result = t(key);
    return typeof result === 'string' ? result : defaultValue;
  };
  const [searchValue, setSearchValue] = useState<string>('');
  const [systemPresets, setSystemPresets] = useState<EnvironmentResponseRule[]>([]);
  const [filteredPresets, setFilteredPresets] = useState<EnvironmentResponseRule[]>([]);
  const [filteredCustomPresets, setFilteredCustomPresets] = useState<EnvironmentResponseRule[]>([]);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editingPreset, setEditingPreset] = useState<EnvironmentResponseRule | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  // 加载系统预设
  useEffect(() => {
    // 创建模拟的系统预设
    const presets: EnvironmentResponseRule[] = [
      {
        id: 'rainy_weather_response',
        name: '雨天响应',
        description: '角色在雨天的响应行为',
        responseType: ResponseType.ANIMATION,
        priority: ResponsePriority.MEDIUM,
        conditions: [],
        actions: [],
        enabled: true
      },
      {
        id: 'snowy_weather_response',
        name: '雪天响应',
        description: '角色在雪天的响应行为',
        responseType: ResponseType.ANIMATION,
        priority: ResponsePriority.MEDIUM,
        conditions: [],
        actions: [],
        enabled: true
      },
      {
        id: 'hot_weather_response',
        name: '炎热天气响应',
        description: '角色在炎热天气的响应行为',
        responseType: ResponseType.BEHAVIOR,
        priority: ResponsePriority.HIGH,
        conditions: [],
        actions: [],
        enabled: true
      },
      {
        id: 'dark_environment_response',
        name: '黑暗环境响应',
        description: '角色在黑暗环境的响应行为',
        responseType: ResponseType.SOUND,
        priority: ResponsePriority.HIGH,
        conditions: [],
        actions: [],
        enabled: true
      },
      {
        id: 'underwater_response',
        name: '水下环境响应',
        description: '角色在水下环境的响应行为',
        responseType: ResponseType.EFFECT,
        priority: ResponsePriority.CRITICAL,
        conditions: [],
        actions: [],
        enabled: true
      }
    ];
    setSystemPresets(presets);
    filterPresets(presets, customPresets, searchValue, activeCategory);
  }, []);

  // 当自定义预设变化时更新过滤
  useEffect(() => {
    filterPresets(systemPresets, customPresets, searchValue, activeCategory);
  }, [customPresets]);

  /**
   * 过滤预设
   * @param systemPresets 系统预设
   * @param customPresets 自定义预设
   * @param searchValue 搜索值
   * @param category 分类
   */
  const filterPresets = (
    systemPresets: EnvironmentResponseRule[], 
    customPresets: EnvironmentResponseRule[], 
    searchValue: string,
    category: string
  ) => {
    // 过滤系统预设
    let filtered = systemPresets;
    
    // 按分类过滤
    if (category !== 'all') {
      filtered = filtered.filter(preset => {
        if (category === 'animation' && preset.responseType === ResponseType.ANIMATION) return true;
        if (category === 'sound' && preset.responseType === ResponseType.SOUND) return true;
        if (category === 'effect' && preset.responseType === ResponseType.EFFECT) return true;
        if (category === 'behavior' && preset.responseType === ResponseType.BEHAVIOR) return true;
        if (category === 'high' && preset.priority === ResponsePriority.HIGH) return true;
        if (category === 'critical' && preset.priority === ResponsePriority.CRITICAL) return true;
        return false;
      });
    }
    
    // 按搜索值过滤
    if (searchValue) {
      const lowerSearchValue = searchValue.toLowerCase();
      filtered = filtered.filter(preset => 
        preset.name.toLowerCase().includes(lowerSearchValue) || 
        (preset.description && preset.description.toLowerCase().includes(lowerSearchValue))
      );
    }
    
    setFilteredPresets(filtered);
    
    // 过滤自定义预设
    let filteredCustom = customPresets;
    
    // 按分类过滤
    if (category !== 'all') {
      filteredCustom = filteredCustom.filter(preset => {
        if (category === 'animation' && preset.responseType === ResponseType.ANIMATION) return true;
        if (category === 'sound' && preset.responseType === ResponseType.SOUND) return true;
        if (category === 'effect' && preset.responseType === ResponseType.EFFECT) return true;
        if (category === 'behavior' && preset.responseType === ResponseType.BEHAVIOR) return true;
        if (category === 'high' && preset.priority === ResponsePriority.HIGH) return true;
        if (category === 'critical' && preset.priority === ResponsePriority.CRITICAL) return true;
        return false;
      });
    }
    
    // 按搜索值过滤
    if (searchValue) {
      const lowerSearchValue = searchValue.toLowerCase();
      filteredCustom = filteredCustom.filter(preset => 
        preset.name.toLowerCase().includes(lowerSearchValue) || 
        (preset.description && preset.description.toLowerCase().includes(lowerSearchValue))
      );
    }
    
    setFilteredCustomPresets(filteredCustom);
  };

  /**
   * 处理搜索
   * @param value 搜索值
   */
  const handleSearch = (value: string) => {
    setSearchValue(value);
    filterPresets(systemPresets, customPresets, value, activeCategory);
  };

  /**
   * 处理分类变更
   * @param category 分类
   */
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    filterPresets(systemPresets, customPresets, searchValue, category);
  };

  /**
   * 处理应用预设
   * @param preset 预设
   */
  const handleApplyPreset = (preset: EnvironmentResponseRule) => {
    if (onApplyPreset) {
      onApplyPreset(preset);
      message.success(translate('environment.presetApplied', '预设已应用'));
    }
  };

  /**
   * 处理创建自定义预设
   */
  const handleCreateCustomPreset = () => {
    setEditingPreset(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  /**
   * 处理编辑自定义预设
   * @param preset 预设
   */
  const handleEditCustomPreset = (preset: EnvironmentResponseRule) => {
    setEditingPreset(preset);
    setIsModalVisible(true);
    form.setFieldsValue({
      name: preset.name,
      description: preset.description,
      responseType: preset.responseType,
      priority: preset.priority
    });
  };

  /**
   * 处理复制预设
   * @param preset 预设
   */
  const handleDuplicatePreset = (preset: EnvironmentResponseRule) => {
    const newPreset: EnvironmentResponseRule = {
      ...preset,
      id: `custom_preset_${Date.now()}`,
      name: `${preset.name} (${translate('environment.copy', '副本')})`
    };
    
    if (onSaveCustomPreset) {
      onSaveCustomPreset(newPreset);
      message.success(translate('environment.presetDuplicated', '预设已复制'));
    }
  };

  /**
   * 处理删除自定义预设
   * @param presetId 预设ID
   */
  const handleDeleteCustomPreset = (presetId: string) => {
    Modal.confirm({
      title: translate('environment.confirmDelete', '确认删除'),
      content: translate('environment.confirmDeletePresetMessage', '确定要删除这个预设吗？'),
      okText: translate('environment.delete', '删除'),
      okType: 'danger',
      cancelText: translate('environment.cancel', '取消'),
      onOk: () => {
        if (onDeleteCustomPreset) {
          onDeleteCustomPreset(presetId);
          message.success(translate('environment.presetDeleted', '预设已删除'));
        }
      }
    });
  };

  /**
   * 处理保存自定义预设
   */
  const handleSaveCustomPreset = () => {
    form.validateFields().then(values => {
      const newPreset: EnvironmentResponseRule = {
        id: editingPreset ? editingPreset.id : `custom_preset_${Date.now()}`,
        name: values.name,
        description: values.description,
        responseType: values.responseType,
        priority: values.priority,
        conditions: editingPreset ? editingPreset.conditions : [],
        actions: editingPreset ? editingPreset.actions : [],
        enabled: true
      };
      
      if (onSaveCustomPreset) {
        onSaveCustomPreset(newPreset);
        setIsModalVisible(false);
        message.success(translate('environment.presetSaved', '预设已保存'));
      }
    });
  };

  /**
   * 渲染预设列表项
   * @param preset 预设
   * @param isCustom 是否自定义预设
   */
  const renderPresetItem = (preset: EnvironmentResponseRule, isCustom: boolean = false) => {
    return (
      <List.Item
        key={preset.id}
        actions={[
          <Tooltip title={translate('environment.apply', '应用')}>
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => handleApplyPreset(preset)}
            >
              {translate('environment.apply', '应用')}
            </Button>
          </Tooltip>,
          <Tooltip title={translate('environment.duplicate', '复制')}>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleDuplicatePreset(preset)}
            />
          </Tooltip>,
          isCustom && (
            <Tooltip title={translate('environment.edit', '编辑')}>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEditCustomPreset(preset)}
              />
            </Tooltip>
          ),
          isCustom && (
            <Tooltip title={translate('environment.delete', '删除')}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteCustomPreset(preset.id)}
              />
            </Tooltip>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <Space>
              <span>{preset.name}</span>
              {isCustom && <Tag color="green">{translate('environment.custom', '自定义')}</Tag>}
              <Tag color={getResponseTypeColor(preset.responseType)}>{preset.responseType}</Tag>
              <Tag color={getPriorityColor(preset.priority)}>{getPriorityLabel(preset.priority)}</Tag>
            </Space>
          }
          description={preset.description}
        />
        <div className="preset-details">
          <Space>
            <Badge count={preset.conditions.length} overflowCount={99} style={{ backgroundColor: '#108ee9' }}>
              <Tag>{translate('environment.conditions', '条件')}</Tag>
            </Badge>
            <Badge count={preset.actions.length} overflowCount={99} style={{ backgroundColor: '#87d068' }}>
              <Tag>{translate('environment.actions', '动作')}</Tag>
            </Badge>
          </Space>
        </div>
      </List.Item>
    );
  };

  /**
   * 获取响应类型颜色
   * @param type 响应类型
   * @returns 颜色
   */
  const getResponseTypeColor = (type: string): string => {
    switch (type) {
      case ResponseType.ANIMATION:
        return 'blue';
      case ResponseType.EFFECT:
        return 'purple';
      case ResponseType.SOUND:
        return 'cyan';
      case ResponseType.BEHAVIOR:
        return 'orange';
      default:
        return 'default';
    }
  };

  /**
   * 获取优先级颜色
   * @param priority 优先级
   * @returns 颜色
   */
  const getPriorityColor = (priority: number): string => {
    switch (priority) {
      case ResponsePriority.LOW:
        return 'green';
      case ResponsePriority.MEDIUM:
        return 'blue';
      case ResponsePriority.HIGH:
        return 'orange';
      case ResponsePriority.CRITICAL:
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取优先级标签
   * @param priority 优先级
   * @returns 标签
   */
  const getPriorityLabel = (priority: number): string => {
    switch (priority) {
      case ResponsePriority.LOW:
        return translate('environment.priorities.low', '低');
      case ResponsePriority.MEDIUM:
        return translate('environment.priorities.medium', '中');
      case ResponsePriority.HIGH:
        return translate('environment.priorities.high', '高');
      case ResponsePriority.CRITICAL:
        return translate('environment.priorities.critical', '紧急');
      default:
        return '';
    }
  };

  return (
    <div className="environment-preset-panel">
      <Card
        title={
          <Space>
            <AppstoreOutlined />
            <span>{translate('environment.presetPanel', '预设面板')}</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateCustomPreset}
          >
            {translate('environment.createCustomPreset', '创建自定义预设')}
          </Button>
        }
      >
        <div className="preset-filters">
          <Search
            placeholder={translate('environment.searchPresets', '搜索预设')}
            allowClear
            enterButton={<SearchOutlined />}
            onSearch={handleSearch}
            style={{ marginBottom: 16 }}
          />
          
          <div className="category-filters">
            <Space wrap>
              <Button
                type={activeCategory === 'all' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('all')}
              >
                {translate('environment.all', '全部')}
              </Button>
              <Button
                type={activeCategory === 'animation' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('animation')}
              >
                {translate('environment.responseTypes.animation', '动画')}
              </Button>
              <Button
                type={activeCategory === 'sound' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('sound')}
              >
                {translate('environment.responseTypes.sound', '声音')}
              </Button>
              <Button
                type={activeCategory === 'effect' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('effect')}
              >
                {translate('environment.responseTypes.effect', '特效')}
              </Button>
              <Button
                type={activeCategory === 'behavior' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('behavior')}
              >
                {translate('environment.responseTypes.behavior', '行为')}
              </Button>
              <Button
                type={activeCategory === 'high' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('high')}
              >
                {translate('environment.priorities.high', '高')}
              </Button>
              <Button
                type={activeCategory === 'critical' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('critical')}
              >
                {translate('environment.priorities.critical', '紧急')}
              </Button>
            </Space>
          </div>
        </div>
        
        <Divider orientation="left">{translate('environment.customPresets', '自定义预设')}</Divider>

        {filteredCustomPresets.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={filteredCustomPresets}
            renderItem={preset => renderPresetItem(preset, true)}
          />
        ) : (
          <div className="empty-custom-presets">
            <p>{translate('environment.noCustomPresets', '暂无自定义预设')}</p>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateCustomPreset}
            >
              {translate('environment.createCustomPreset', '创建自定义预设')}
            </Button>
          </div>
        )}

        <Divider orientation="left">{translate('environment.systemPresets', '系统预设')}</Divider>
        
        <Collapse defaultActiveKey={['1']}>
          <Panel header={translate('environment.systemPresets', '系统预设')} key="1">
            <List
              itemLayout="horizontal"
              dataSource={filteredPresets}
              renderItem={preset => renderPresetItem(preset)}
            />
          </Panel>
        </Collapse>
      </Card>
      
      <Modal
        title={editingPreset ? translate('environment.editPreset', '编辑预设') : translate('environment.createPreset', '创建预设')}
        open={isModalVisible}
        onOk={handleSaveCustomPreset}
        onCancel={() => {
          try {
            setIsModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        okText={translate('environment.save', '保存')}
        cancelText={translate('environment.cancel', '取消')}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={translate('environment.presetName', '预设名称')}
            rules={[{ required: true, message: translate('environment.presetNameRequired', '请输入预设名称') }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label={translate('environment.presetDescription', '预设描述')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="responseType"
            label={translate('environment.responseType', '响应类型')}
            rules={[{ required: true, message: translate('environment.responseTypeRequired', '请选择响应类型') }]}
          >
            <Select>
              <Option value={ResponseType.ANIMATION}>{translate('environment.responseTypes.animation', '动画')}</Option>
              <Option value={ResponseType.EFFECT}>{translate('environment.responseTypes.effect', '特效')}</Option>
              <Option value={ResponseType.SOUND}>{translate('environment.responseTypes.sound', '声音')}</Option>
              <Option value={ResponseType.BEHAVIOR}>{translate('environment.responseTypes.behavior', '行为')}</Option>
              <Option value={ResponseType.CUSTOM}>{translate('environment.responseTypes.custom', '自定义')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label={translate('environment.priority', '优先级')}
            rules={[{ required: true, message: translate('environment.priorityRequired', '请选择优先级') }]}
          >
            <Select>
              <Option value={ResponsePriority.LOW}>{translate('environment.priorities.low', '低')}</Option>
              <Option value={ResponsePriority.MEDIUM}>{translate('environment.priorities.medium', '中')}</Option>
              <Option value={ResponsePriority.HIGH}>{translate('environment.priorities.high', '高')}</Option>
              <Option value={ResponsePriority.CRITICAL}>{translate('environment.priorities.critical', '紧急')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EnvironmentPresetPanel;
