/**
 * 成就通知组件
 */
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Alert, Badge, Card, Typography, Space, Progress } from 'antd';
import { TrophyOutlined, StarOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store';
import { 
  selectRecentUnlocked, 
  selectShowNotification, 
  hideNotification 
} from '../../store/achievements/achievementsSlice';
import { Achievement, AchievementDifficulty } from '../../services/AchievementService';
import './AchievementNotification.css';

const { Title, Text } = Typography;

/**
 * 成就图标组件
 */
const AchievementIcon: React.FC<{ achievement: Achievement }> = ({ achievement }) => {
  switch (achievement.icon) {
    case 'trophy':
      return <TrophyOutlined className="achievement-icon trophy" />;
    case 'star':
      return <StarOutlined className="achievement-icon star" />;
    default:
      return <CheckCircleOutlined className="achievement-icon check" />;
  }
};

/**
 * 成就难度标签组件
 */
const DifficultyBadge: React.FC<{ difficulty: AchievementDifficulty }> = ({ difficulty }) => {
  let color = '';
  
  switch (difficulty) {
    case AchievementDifficulty.EASY:
      color = 'green';
      break;
    case AchievementDifficulty.MEDIUM:
      color = 'blue';
      break;
    case AchievementDifficulty.HARD:
      color = 'purple';
      break;
    case AchievementDifficulty.EXPERT:
      color = 'red';
      break;
  }
  
  return (
    <Badge 
      className="difficulty-badge" 
      color={color} 
      text={<Text className="difficulty-text">{difficulty}</Text>} 
    />
  );
};

/**
 * 成就通知组件
 */
export const AchievementNotification: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const recentUnlocked = useAppSelector(selectRecentUnlocked);
  const showNotification = useAppSelector(selectShowNotification);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visible, setVisible] = useState(false);
  
  // 处理通知显示
  useEffect(() => {
    if (showNotification && recentUnlocked.length > 0) {
      setVisible(true);
      setCurrentIndex(0);
      
      // 5秒后自动隐藏
      const timer = setTimeout(() => {
        setVisible(false);
        dispatch(hideNotification());
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [showNotification, recentUnlocked, dispatch]);
  
  // 如果没有成就或不显示，则不渲染
  if (!visible || recentUnlocked.length === 0) {
    return null;
  }
  
  const achievement = recentUnlocked[currentIndex];
  
  return (
    <div className="achievement-notification-container">
      <Alert
        type="success"
        showIcon={false}
        closable
        onClose={() => {
          try {
            setVisible(false);
            dispatch(hideNotification());
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        message={
          <Card className="achievement-card">
            <Space direction="vertical" size="small" className="achievement-content">
              <div className="achievement-header">
                <AchievementIcon achievement={achievement} />
                <Title level={4} className="achievement-title">
                  {t('achievements.unlocked')}
                </Title>
              </div>
              
              <Title level={3} className="achievement-name">
                {achievement.title}
              </Title>
              
              <Text className="achievement-description">
                {achievement.description}
              </Text>
              
              <div className="achievement-footer">
                <DifficultyBadge difficulty={achievement.difficulty} />
                
                {recentUnlocked.length > 1 && (
                  <div className="achievement-pagination">
                    <Text className="achievement-count">
                      {currentIndex + 1} / {recentUnlocked.length}
                    </Text>
                    <Progress 
                      percent={(currentIndex + 1) / recentUnlocked.length * 100} 
                      showInfo={false} 
                      size="small" 
                      strokeColor="#1890ff" 
                    />
                  </div>
                )}
              </div>
            </Space>
          </Card>
        }
      />
    </div>
  );
};

export default AchievementNotification;
