const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'editor/src/components/achievements/AchievementNotification.tsx',
  'editor/src/components/avatar/ActionEditor.tsx',
  'editor/src/components/avatar/ControllerPresetSelector.tsx',
  'editor/src/components/avatar/EnvironmentAwarenessEditor.tsx',
  'editor/src/components/collaboration/LockPanel.tsx',
  'editor/src/components/collaboration/OrganizationPermissionPanel.tsx',
  'editor/src/components/collaboration/PermissionLogPanel.tsx',
  'editor/src/components/collaboration/SceneVersionComparePanel.tsx',
  'editor/src/components/collaboration/UserInteractionAnalysisPanel.tsx',
  'editor/src/components/common/TextureSelector.tsx',
  'editor/src/components/debug/HistoryPanel.tsx',
  'editor/src/components/debug/PerformanceComparisonPanel.tsx',
  'editor/src/components/debug/PerformanceTestAutomationPanel.tsx',
  'editor/src/components/debug/ServiceStatusMonitor.tsx',
  'editor/src/components/environment/EnvironmentPresetPanel.tsx',
  'editor/src/components/ExampleBrowser/ExampleDetail.tsx',
  'editor/src/components/FacialAnimationEditor/FacialAnimationPresetManager.tsx',
  'editor/src/components/FacialAnimationEditor/FacialAnimationTemplateManager.tsx',
  'editor/src/components/interaction/InteractionEditor.tsx',
  'editor/src/components/layout/MobileAdaptiveLayout.tsx',
  'editor/src/components/network/NetworkLatencySimulator.tsx',
  'editor/src/components/panels/AssetsPanel.tsx',
  'editor/src/components/panels/InstancesPanel.tsx',
  'editor/src/components/panels/LayersPanel.tsx',
  'editor/src/components/physics/PhysicsMaterialEditor.tsx',
  'editor/src/components/physics/PhysicsPresetEditor.tsx',
  'editor/src/components/rag/KnowledgeBasePanel.tsx',
  'editor/src/components/rendering/water/WaterMaterialPresetSelector.tsx',
  'editor/src/components/resources/CharacterResourceBrowser.tsx',
  'editor/src/components/resources/ResourceDependencyManager.tsx',
  'editor/src/components/scene/SceneGenerationPanel.tsx',
  'editor/src/components/scripting/__tests__/EnhancedFeatures.test.tsx',
  'editor/src/components/scripting/ScriptEditor.tsx',
  'editor/src/components/scripting/ScriptTemplates.tsx',
  'editor/src/components/settings/ScriptEditorSettingsPanel.tsx',
  'editor/src/components/terrain/TerrainComponentManager.tsx',
  'editor/src/components/terrain/TerrainImportExportPanel.tsx',
  'editor/src/components/testing/UserTestingPanel.tsx',
  'editor/src/components/tutorials/ProjectTutorialPanel.tsx',
  'editor/src/components/tutorials/TutorialSeriesPanel.tsx',
  'editor/src/components/ui/UIPresetSelector.tsx',
  'editor/src/components/visualscript/DebugPanel.tsx',
  'editor/src/pages/Editor/index.tsx',
  'editor/src/pages/ProjectsPage.tsx',
  'editor/src/panels/FacialAnimationPresetPanel.tsx',
  'editor/src/tools/TutorialRecordingTool.tsx'
];

function fixModalSyntaxErrors(content) {
  // 修复 Modal onCancel 属性语法错误
  // 模式1: onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> { ... }}
  content = content.replace(
    /onCancel=\{\(\) = destroyOnClose keyboard=\{true\} maskClosable=\{true\}> \{([^}]+)\}\}/g,
    'onCancel={() => {$1}} destroyOnClose keyboard={true} maskClosable={true}'
  );

  // 修复 Button icon 属性语法错误
  // 模式2: icon={<ImportOutlined / destroyOnClose keyboard={true} maskClosable={true}>}
  content = content.replace(
    /icon=\{<(\w+) \/ destroyOnClose keyboard=\{true\} maskClosable=\{true\}>\}/g,
    'icon={<$1 />}'
  );

  // 修复 onOk 属性语法错误
  // 模式3: onOk={() = destroyOnClose keyboard={true} maskClosable={true}> { ... }}
  content = content.replace(
    /onOk=\{\(\) = destroyOnClose keyboard=\{true\} maskClosable=\{true\}> \{([^}]+)\}\}/g,
    'onOk={() => {$1}} destroyOnClose keyboard={true} maskClosable={true}'
  );

  // 修复其他可能的语法错误
  content = content.replace(
    /onCancel=\{\(\) = ([^>]+)> \{/g,
    'onCancel={() => {'
  );

  content = content.replace(
    /onOk=\{\(\) = ([^>]+)> \{/g,
    'onOk={() => {'
  );

  // 修复特殊的语法错误 - 多余的大括号
  content = content.replace(
    /try \{\s*\{\s*([^}]+)\s*\} catch/g,
    'try {\n            $1\n          } catch'
  );

  return content;
}

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`文件不存在: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixModalSyntaxErrors(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ 修复完成: ${filePath}`);
      return true;
    } else {
      console.log(`⚪ 无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复失败: ${filePath}`, error.message);
    return false;
  }
}

function main() {
  console.log('开始批量修复 Modal 语法错误...\n');
  
  let fixedCount = 0;
  let totalCount = 0;

  for (const file of filesToFix) {
    totalCount++;
    if (fixFile(file)) {
      fixedCount++;
    }
  }

  console.log(`\n修复完成！`);
  console.log(`总文件数: ${totalCount}`);
  console.log(`修复文件数: ${fixedCount}`);
  console.log(`跳过文件数: ${totalCount - fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { fixModalSyntaxErrors, fixFile };
